{"commands": [{"id": "X7B5YyS3lsCN9H9jloBSB", "name": "song", "response": "🎵 Now Playing: \"{track}\" by {artist} - requested by {requester} [{currentTime}/{duration}]", "userLevel": "everyone", "cooldown": 5, "enabled": true, "usageCount": 4}], "giveaways": {"active": null, "past": [{"id": "padlzKdwOqsfG7FMk1hHB", "title": "!sr", "prize": "gay", "keyword": "!join", "startTime": "2025-05-30T13:06:58.685Z", "endTime": "2025-05-30T13:11:58.685Z", "timeRemaining": "0:01", "isPaused": false, "entries": [{"username": "TestUser1", "timestamp": "1:07:00 PM"}, {"username": "TestUser2", "timestamp": "1:07:00 PM"}, {"username": "TestUser3", "timestamp": "1:07:00 PM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "1:07:09 PM", "message": "!join", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}], "eligibleEntries": [{"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "1:07:09 PM", "message": "!join", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}], "restrictions": {"subscribersOnly": false, "followersOnly": false, "excludeModerators": false, "excludeVips": false, "minFollowAge": 0, "minAccountAge": 0, "maxEntries": 1000, "allowMultipleEntries": false}, "announceStart": true, "startMessage": "🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉", "winnerMessage": "Congrats {winner}, you just won {prize}!", "winner": {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "1:07:09 PM", "message": "!join", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}}, {"id": "OkEY7HB1nzamWf0DwnDAo", "title": "im gay", "prize": "gayness", "keyword": "im super hella gay come fuck me stepbro", "startTime": "2025-05-30T12:58:21.794Z", "endTime": "2025-05-30T13:03:21.794Z", "timeRemaining": "0:01", "isPaused": false, "entries": [{"username": "TestUser1", "timestamp": "12:58:23 PM"}, {"username": "TestUser2", "timestamp": "12:58:23 PM"}, {"username": "TestUser3", "timestamp": "12:58:23 PM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "12:58:38 PM", "message": "im super hella gay come fuck me stepbro", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}], "eligibleEntries": [{"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "12:58:38 PM", "message": "im super hella gay come fuck me stepbro", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}], "restrictions": {"subscribersOnly": false, "followersOnly": false, "excludeModerators": false, "excludeVips": false, "minFollowAge": 0, "minAccountAge": 0, "maxEntries": 1000, "allowMultipleEntries": false}, "announceStart": true, "startMessage": "🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉", "winnerMessage": "Congrats {winner}, you just won {prize}!", "winner": {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "12:58:38 PM", "message": "im super hella gay come fuck me stepbro", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}}, {"id": "ilWSgADEIvTdym8N5lVoB", "title": "balls", "prize": "balls", "keyword": "!join", "startTime": "2025-05-30T12:52:03.615Z", "endTime": "2025-05-30T12:57:03.615Z", "timeRemaining": "0:01", "isPaused": false, "entries": [{"username": "TestUser1", "timestamp": "12:52:05 PM"}, {"username": "TestUser2", "timestamp": "12:52:05 PM"}, {"username": "TestUser3", "timestamp": "12:52:05 PM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "12:52:11 PM", "message": "!join", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}], "eligibleEntries": [{"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "12:52:11 PM", "message": "!join", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}], "restrictions": {"subscribersOnly": false, "followersOnly": false, "excludeModerators": false, "excludeVips": false, "minFollowAge": 0, "minAccountAge": 0, "maxEntries": 1000, "allowMultipleEntries": false}, "announceStart": true, "startMessage": "🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉", "winnerMessage": "Congrats {winner}, you just won {prize}!", "winner": {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "12:52:11 PM", "message": "!join", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}}]}, "songs": {"queue": [], "current": {"id": "song_YExhhY8wkGDj9TVQT1vn_", "title": "<PERSON> - Summertime Sadness (Lyrics)", "thumbnail": "https://i.ytimg.com/vi/HQOQ52FkRzQ/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLDZNxsFD0NpWoqWTf6FCPn-HkPNcA", "duration": 265, "requestedBy": "You", "source": "youtube", "sourceId": "HQOQ52FkRzQ", "url": "https://piped.video/watch?v=HQOQ52FkRzQ", "currentTime": 0}, "history": [{"id": "song_t3GalG4O5af99zseRTbif", "title": "<PERSON> - Summertime Sadness (Lyrics)", "thumbnail": "https://i.ytimg.com/vi/HQOQ52FkRzQ/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLDZNxsFD0NpWoqWTf6FCPn-HkPNcA", "duration": 265, "requestedBy": "You", "source": "youtube", "sourceId": "HQOQ52FkRzQ", "url": "https://piped.video/watch?v=HQOQ52FkRzQ", "currentTime": 265}, {"id": "song_HBW3lgaRibbuzJBV4w3GQ", "title": "Runaway", "thumbnail": "https://i.ytimg.com/vi/EMnQwBTJnMM/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLCDQXuGZ2A7x0yrqr620Y2TeLr6Fg", "duration": 548, "requestedBy": "You", "source": "youtube", "sourceId": "EMnQwBTJnMM", "url": "https://piped.video/watch?v=EMnQwBTJnMM", "currentTime": 95}, {"id": "song_iRRBJ8L-pC45BlF5Uid-_", "title": "Vengaboys - Boom, Boom, Boom, Boom!!", "thumbnail": "https://i.ytimg.com/vi/llyiQ4I-mcQ/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAlA6M2jUQ1CFIJYlzMgrYyy-zoIA", "duration": 204, "requestedBy": "You", "source": "youtube", "sourceId": "llyiQ4I-mcQ", "url": "https://piped.video/watch?v=llyiQ4I-mcQ", "currentTime": 204}, {"id": "song_BsYojee-2fGEzaBMZ0ltd", "title": "Runaway", "thumbnail": "https://i.ytimg.com/vi/EMnQwBTJnMM/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLCDQXuGZ2A7x0yrqr620Y2TeLr6Fg", "duration": 548, "requestedBy": "You", "source": "youtube", "sourceId": "EMnQwBTJnMM", "url": "https://piped.video/watch?v=EMnQwBTJnMM", "currentTime": 17}, {"id": "song_Pj2KdQ9JVHc1q_nlQ001P", "title": "S3RL - Shoulder Boulders HQ", "thumbnail": "https://i.ytimg.com/vi/jofBpB0OkcY/hq2.jpg?sqp=-oaymwE9COADEI4CSFryq4qpAy8IARUAAAAAGAElAADIQj0AgKJDeAHwAQH4Af4JgALQBYoCDAgAEAEYRyBJKFkwDw==&rs=AOn4CLAu4oMmErthtgWxTY1vQf1xoXEMsw", "duration": 270, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "jofBpB0OkcY", "url": "https://piped.video/watch?v=jofBpB0OkcY", "currentTime": 270}, {"id": "song_4YyjOKB9pfxGEIaBt5kPI", "title": "S3RL - Shoulder Boulders HQ", "thumbnail": "https://i.ytimg.com/vi/jofBpB0OkcY/hq2.jpg?sqp=-oaymwE9COADEI4CSFryq4qpAy8IARUAAAAAGAElAADIQj0AgKJDeAHwAQH4Af4JgALQBYoCDAgAEAEYRyBJKFkwDw==&rs=AOn4CLAu4oMmErthtgWxTY1vQf1xoXEMsw", "duration": 270, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "jofBpB0OkcY", "url": "https://piped.video/watch?v=jofBpB0OkcY", "currentTime": 70}]}, "chat": {"messages": [{"id": "qA8KAxWsROVn2YEKGq8Oh", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748609286905, "isMod": false, "isSubscriber": false}, {"id": "z5PMNfFb67imUh1mPYXl4", "username": "streamelements", "message": "@lordwolfyy<PERSON> failed to get the current song.", "color": "#5B99FF", "timestamp": 1748609287111, "isMod": true, "isSubscriber": false}, {"id": "BF8CU7NUi0agal7UqepEE", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr shoulder bolders", "color": "#48E848", "timestamp": 1748609359542, "isMod": false, "isSubscriber": false}, {"id": "PFqZp1nttZbxCwoy42C6i", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748609360007, "isMod": true, "isSubscriber": false}, {"id": "0wkGsrJSmTipB1OvslQeR", "username": "streamelements", "message": "@lordwolfyyy, added S3RL - \"Shoulder Boulders - S3RL\" to the queue at #37 (playing ~in  1 hour 46 mins) https://youtu.be/-VqDHb6B8xo", "color": "#5B99FF", "timestamp": 1748609360201, "isMod": true, "isSubscriber": false}, {"id": "QFP837y_Lr5OCxn46segg", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748609365141, "isMod": false, "isSubscriber": false}, {"id": "iGdMfNoVt3YBg9brMhhGa", "username": "streamelements", "message": "@lordwolfyy<PERSON> failed to get the current song.", "color": "#5B99FF", "timestamp": 1748609365323, "isMod": true, "isSubscriber": false}, {"id": "B81J8-N-l53Yoq4bqxdWg", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!son", "color": "#48E848", "timestamp": 1748609380957, "isMod": false, "isSubscriber": false}, {"id": "0nQyex5wSFGTrcGZ5qLsF", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748609382306, "isMod": false, "isSubscriber": false}, {"id": "I0J85PKFUCS1vtS7CK9mx", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr soulder boulders", "color": "#48E848", "timestamp": 1748609388394, "isMod": false, "isSubscriber": false}, {"id": "7akMLV-_ui3kDrxfs5t2J", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748609388996, "isMod": true, "isSubscriber": false}, {"id": "bJwK1MZG9yU-jvo9KT8Sr", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!skip", "color": "#48E848", "timestamp": 1748609393293, "isMod": false, "isSubscriber": false}, {"id": "J4nokvGj2x4gvOVorjd0x", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748609446029, "isMod": false, "isSubscriber": false}, {"id": "yhrx9c4aqyrmjUxaqtvS9", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!bottest", "color": "#48E848", "timestamp": 1748609448828, "isMod": false, "isSubscriber": false}, {"id": "hED9ujRLiQY8c3zJCeuMV", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!queueu", "color": "#48E848", "timestamp": 1748609480094, "isMod": false, "isSubscriber": false}, {"id": "lzHj92xo_br0kKdou9577", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!queue", "color": "#48E848", "timestamp": 1748609483217, "isMod": false, "isSubscriber": false}, {"id": "efsvNZTgPlC1Ky5wHIWKP", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748609531606, "isMod": false, "isSubscriber": false}, {"id": "8kKFoe4YmkHLvL03SvnMa", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "yay", "color": "#48E848", "timestamp": 1748609576595, "isMod": false, "isSubscriber": false}, {"id": "QBwp2D-jeD4iHeAyCbBL-", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748609601435, "isMod": false, "isSubscriber": false}, {"id": "2zOsUltW2OFKGD3OqDfJ0", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "homosexual", "color": "#48E848", "timestamp": 1748609761826, "isMod": false, "isSubscriber": false}, {"id": "LbVpDx0nu_TxBjh1rPjIq", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "im a gay homosexual", "color": "#48E848", "timestamp": 1748609785750, "isMod": false, "isSubscriber": false}, {"id": "sEKes_2H-JXCm84TxCmkB", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "f;ldjbdf", "color": "#48E848", "timestamp": 1748609794933, "isMod": false, "isSubscriber": false}, {"id": "h2IZ_xCQi6U5zXJeba-8w", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "im super hella gay come fuck me stepbro", "color": "#48E848", "timestamp": 1748609918739, "isMod": false, "isSubscriber": false}, {"id": "_5B8FmY4DGQA_mET9g8w5", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "im super hella gay come fuck me stepbro", "color": "#48E848", "timestamp": 1748609930527, "isMod": false, "isSubscriber": false}, {"id": "b82QY6beuH9bjSx2zjaoa", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "my balls itch stepbro", "color": "#48E848", "timestamp": 1748609961490, "isMod": false, "isSubscriber": false}, {"id": "R8DClap81LgIlBMyYSEze", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "can you come itch them for me", "color": "#48E848", "timestamp": 1748609964725, "isMod": false, "isSubscriber": false}, {"id": "TFcFJMQoEuYkx2pyeFMPv", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "<3", "color": "#48E848", "timestamp": 1748609966534, "isMod": false, "isSubscriber": false}, {"id": "v3WSdxZ21Tddj7tHa7dh1", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "sfdkjfdgljf", "color": "#48E848", "timestamp": 1748609997802, "isMod": false, "isSubscriber": false}, {"id": "6_DKcRm4ZVuck2FYfrmJz", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "kgdlgksjglkd", "color": "#48E848", "timestamp": 1748610005430, "isMod": false, "isSubscriber": false}, {"id": "9cbs6U1J035bNxpXMoSRY", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gsdkgd", "color": "#48E848", "timestamp": 1748610208694, "isMod": false, "isSubscriber": false}, {"id": "iIv4JSMGuDJPIqXnO-qTm", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "i won it", "color": "#48E848", "timestamp": 1748610216937, "isMod": false, "isSubscriber": false}, {"id": "DWOcuWOuAucmtSZ9z0iww", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748610217668, "isMod": false, "isSubscriber": false}, {"id": "2MNU3d86R7I7MPPgzdsR3", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748610429320, "isMod": false, "isSubscriber": false}, {"id": "GYN-x9fibMbmzf2C115xj", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr", "color": "#48E848", "timestamp": 1748610760741, "isMod": false, "isSubscriber": false}, {"id": "PE5yS3AoYzvLvt8xLwZ5Z", "username": "nightbot", "message": "@lord<PERSON>yyy -> You must specify a song to search for.", "color": "#7C7CE1", "timestamp": 1748610760857, "isMod": true, "isSubscriber": false}, {"id": "tbYeJ9tR99ZKYZ5rTxYff", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!skip", "color": "#48E848", "timestamp": 1748610836584, "isMod": false, "isSubscriber": false}, {"id": "RY9p7wcbO8IYLvr-v3MFI", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr runaway kanye west", "color": "#48E848", "timestamp": 1748611086878, "isMod": false, "isSubscriber": false}, {"id": "dTEg0nW3qqrH3VT8vKGih", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748611087438, "isMod": true, "isSubscriber": false}, {"id": "buaGQUvV6BeTvAv0Fxad6", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr runaway", "color": "#48E848", "timestamp": 1748611109638, "isMod": false, "isSubscriber": false}, {"id": "eRuQbudZRsmHng09GWDFW", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748611110062, "isMod": true, "isSubscriber": false}]}, "metrics": {"totalChatMessages": 40, "totalCommands": 4, "totalSongRequests": 9, "startTime": 1748609276545}, "settings": {}}