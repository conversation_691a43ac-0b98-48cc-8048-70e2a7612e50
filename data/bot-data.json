{"commands": [{"id": "X7B5YyS3lsCN9H9jloBSB", "name": "song", "response": "🎵 Now Playing: \"{track}\" by {artist} - requested by {requester} [{currentTime}/{duration}]", "userLevel": "everyone", "cooldown": 5, "enabled": true, "usageCount": 4}], "giveaways": {"active": {"id": "ilWSgADEIvTdym8N5lVoB", "title": "balls", "prize": "balls", "keyword": "!join", "startTime": "2025-05-30T12:52:03.615Z", "endTime": "2025-05-30T12:57:03.615Z", "timeRemaining": "3:11", "isPaused": false, "entries": [{"username": "TestUser1", "timestamp": "12:52:05 PM"}, {"username": "TestUser2", "timestamp": "12:52:05 PM"}, {"username": "TestUser3", "timestamp": "12:52:05 PM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "12:52:11 PM", "message": "!join", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}], "eligibleEntries": [{"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "12:52:11 PM", "message": "!join", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}], "restrictions": {"subscribersOnly": false, "followersOnly": false, "excludeModerators": false, "excludeVips": false, "minFollowAge": 0, "minAccountAge": 0, "maxEntries": 1000, "allowMultipleEntries": false}, "announceStart": true, "startMessage": "🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉", "winnerMessage": "Congrats {winner}, you just won {prize}!", "winner": {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "12:52:11 PM", "message": "!join", "userType": "viewer", "badges": [], "isEligible": true, "userId": "*********"}}, "past": []}, "songs": {"queue": [], "current": {"id": "song_Pj2KdQ9JVHc1q_nlQ001P", "title": "S3RL - Shoulder Boulders HQ", "thumbnail": "https://i.ytimg.com/vi/jofBpB0OkcY/hq2.jpg?sqp=-oaymwE9COADEI4CSFryq4qpAy8IARUAAAAAGAElAADIQj0AgKJDeAHwAQH4Af4JgALQBYoCDAgAEAEYRyBJKFkwDw==&rs=AOn4CLAu4oMmErthtgWxTY1vQf1xoXEMsw", "duration": 270, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "jofBpB0OkcY", "url": "https://piped.video/watch?v=jofBpB0OkcY", "currentTime": 201}, "history": [{"id": "song_4YyjOKB9pfxGEIaBt5kPI", "title": "S3RL - Shoulder Boulders HQ", "thumbnail": "https://i.ytimg.com/vi/jofBpB0OkcY/hq2.jpg?sqp=-oaymwE9COADEI4CSFryq4qpAy8IARUAAAAAGAElAADIQj0AgKJDeAHwAQH4Af4JgALQBYoCDAgAEAEYRyBJKFkwDw==&rs=AOn4CLAu4oMmErthtgWxTY1vQf1xoXEMsw", "duration": 270, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "jofBpB0OkcY", "url": "https://piped.video/watch?v=jofBpB0OkcY", "currentTime": 70}]}, "chat": {"messages": [{"id": "qA8KAxWsROVn2YEKGq8Oh", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748609286905, "isMod": false, "isSubscriber": false}, {"id": "z5PMNfFb67imUh1mPYXl4", "username": "streamelements", "message": "@lordwolfyy<PERSON> failed to get the current song.", "color": "#5B99FF", "timestamp": 1748609287111, "isMod": true, "isSubscriber": false}, {"id": "BF8CU7NUi0agal7UqepEE", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr shoulder bolders", "color": "#48E848", "timestamp": 1748609359542, "isMod": false, "isSubscriber": false}, {"id": "PFqZp1nttZbxCwoy42C6i", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748609360007, "isMod": true, "isSubscriber": false}, {"id": "0wkGsrJSmTipB1OvslQeR", "username": "streamelements", "message": "@lordwolfyyy, added S3RL - \"Shoulder Boulders - S3RL\" to the queue at #37 (playing ~in  1 hour 46 mins) https://youtu.be/-VqDHb6B8xo", "color": "#5B99FF", "timestamp": 1748609360201, "isMod": true, "isSubscriber": false}, {"id": "QFP837y_Lr5OCxn46segg", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748609365141, "isMod": false, "isSubscriber": false}, {"id": "iGdMfNoVt3YBg9brMhhGa", "username": "streamelements", "message": "@lordwolfyy<PERSON> failed to get the current song.", "color": "#5B99FF", "timestamp": 1748609365323, "isMod": true, "isSubscriber": false}, {"id": "B81J8-N-l53Yoq4bqxdWg", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!son", "color": "#48E848", "timestamp": 1748609380957, "isMod": false, "isSubscriber": false}, {"id": "0nQyex5wSFGTrcGZ5qLsF", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748609382306, "isMod": false, "isSubscriber": false}, {"id": "I0J85PKFUCS1vtS7CK9mx", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr soulder boulders", "color": "#48E848", "timestamp": 1748609388394, "isMod": false, "isSubscriber": false}, {"id": "7akMLV-_ui3kDrxfs5t2J", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748609388996, "isMod": true, "isSubscriber": false}, {"id": "bJwK1MZG9yU-jvo9KT8Sr", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!skip", "color": "#48E848", "timestamp": 1748609393293, "isMod": false, "isSubscriber": false}, {"id": "J4nokvGj2x4gvOVorjd0x", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748609446029, "isMod": false, "isSubscriber": false}, {"id": "yhrx9c4aqyrmjUxaqtvS9", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!bottest", "color": "#48E848", "timestamp": 1748609448828, "isMod": false, "isSubscriber": false}, {"id": "hED9ujRLiQY8c3zJCeuMV", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!queueu", "color": "#48E848", "timestamp": 1748609480094, "isMod": false, "isSubscriber": false}, {"id": "lzHj92xo_br0kKdou9577", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!queue", "color": "#48E848", "timestamp": 1748609483217, "isMod": false, "isSubscriber": false}, {"id": "efsvNZTgPlC1Ky5wHIWKP", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748609531606, "isMod": false, "isSubscriber": false}, {"id": "8kKFoe4YmkHLvL03SvnMa", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "yay", "color": "#48E848", "timestamp": 1748609576595, "isMod": false, "isSubscriber": false}, {"id": "QBwp2D-jeD4iHeAyCbBL-", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748609601435, "isMod": false, "isSubscriber": false}]}, "metrics": {"totalChatMessages": 19, "totalCommands": 4, "totalSongRequests": 2, "startTime": 1748609276545}, "settings": {}}