{"commands": [{"id": "default-discord", "name": "discord", "response": "Join our Discord server at: https://discord.gg/example", "userLevel": "everyone", "cooldown": 10, "enabled": true, "usageCount": 1}, {"id": "default-uptime", "name": "uptime", "response": "Stream has been live for {uptime}", "userLevel": "everyone", "cooldown": 5, "enabled": true, "usageCount": 0}], "giveaways": {"active": null, "past": [{"id": "H7eNdgFD1I4UDvdbk5ZPC", "title": "ballsack", "prize": "my ball hairs", "keyword": "!join", "eligibility": "everyone", "startTime": "2025-05-30T11:58:12.743Z", "endTime": "2025-05-30T12:03:12.743Z", "timeRemaining": "4:48", "isPaused": false, "entries": [{"username": "TestUser1", "timestamp": "11:58:14 AM"}, {"username": "TestUser2", "timestamp": "11:58:14 AM"}, {"username": "TestUser3", "timestamp": "11:58:14 AM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "11:58:19 AM"}, {"username": "TestUser501", "timestamp": "11:58:24 AM"}], "winners": [], "announceStart": true, "startMessage": "🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉"}, {"id": "1HKkZmq6RWSYGtERWQy2D", "title": "my balls", "prize": "my balls", "keyword": "!join", "eligibility": "everyone", "startTime": "2025-05-30T11:38:50.037Z", "endTime": "2025-05-30T11:43:50.037Z", "timeRemaining": "4:18", "isPaused": false, "entries": [{"username": "TestUser1", "timestamp": "11:38:52 AM"}, {"username": "TestUser2", "timestamp": "11:38:52 AM"}, {"username": "TestUser3", "timestamp": "11:38:52 AM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "11:38:55 AM"}, {"username": "TestUser602", "timestamp": "11:39:02 AM"}], "winners": [], "announceStart": true, "startMessage": "🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉"}, {"id": "iMeD8ItoBhnTWDGQlfYf8", "title": "my balls", "prize": "my balls", "keyword": "!join", "eligibility": "everyone", "startTime": "2025-05-30T11:38:04.735Z", "endTime": "2025-05-30T11:43:04.735Z", "timeRemaining": "4:28", "isPaused": false, "entries": [{"username": "TestUser1", "timestamp": "11:38:06 AM"}, {"username": "TestUser2", "timestamp": "11:38:06 AM"}, {"username": "TestUser3", "timestamp": "11:38:06 AM"}, {"username": "TestUser776", "timestamp": "11:38:10 AM"}, {"username": "TestUser181", "timestamp": "11:38:11 AM"}, {"username": "TestUser852", "timestamp": "11:38:11 AM"}, {"username": "TestUser416", "timestamp": "11:38:11 AM"}, {"username": "TestUser382", "timestamp": "11:38:11 AM"}, {"username": "TestUser164", "timestamp": "11:38:11 AM"}, {"username": "TestUser893", "timestamp": "11:38:12 AM"}, {"username": "TestUser738", "timestamp": "11:38:12 AM"}, {"username": "TestUser842", "timestamp": "11:38:13 AM"}, {"username": "TestUser343", "timestamp": "11:38:13 AM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "11:38:24 AM"}], "winners": [], "announceStart": true, "startMessage": "🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉"}, {"id": "2qOTicaRAlaV8R7SZXQ5V", "title": "gay", "prize": "gayness", "keyword": "!join", "eligibility": "everyone", "startTime": "2025-05-29T19:44:33.893Z", "endTime": "2025-05-29T19:49:33.893Z", "timeRemaining": "4:28", "isPaused": false, "entries": [{"username": "TestUser1", "timestamp": "7:44:35 PM"}, {"username": "TestUser2", "timestamp": "7:44:35 PM"}, {"username": "TestUser3", "timestamp": "7:44:35 PM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "7:44:42 PM"}], "winners": [{"username": "TestUser3", "timestamp": "7:44:45 PM"}, {"username": "TestUser1", "timestamp": "7:44:54 PM"}, {"username": "TestUser2", "timestamp": "7:44:55 PM"}, {"username": "TestUser3", "timestamp": "7:44:56 PM"}, {"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "7:44:58 PM"}], "announceStart": true, "startMessage": "🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉"}, {"id": "iizu8T6DLVHnI99rciymm", "title": "ballsack", "prize": "ballsack", "keyword": "!join", "eligibility": "everyone", "startTime": "2025-05-29T19:32:54.145Z", "endTime": "2025-05-29T19:37:54.145Z", "timeRemaining": "0:10", "isPaused": false, "entries": [{"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "7:33:51 PM"}], "winners": []}, {"id": "kxVcnuLbt3_2khvlTFRWL", "title": "ballsack", "prize": "ballsack", "keyword": "!join", "eligibility": "everyone", "startTime": "2025-05-29T19:32:54.144Z", "endTime": "2025-05-29T19:37:54.144Z", "timeRemaining": "5:00", "isPaused": false, "entries": [], "winners": []}, {"id": "yIixPYaaVvrismJnX2AZH", "title": "gw", "prize": "gw", "keyword": "gw", "eligibility": "everyone", "startTime": "2025-05-29T19:26:48.728Z", "endTime": "2025-05-29T19:31:48.728Z", "timeRemaining": "0:01", "isPaused": false, "entries": [{"username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timestamp": "7:26:51 PM"}], "winners": []}, {"id": "HBfIyBsKUpsoMaQlbUGh_", "title": "gw", "prize": "gw", "keyword": "gw", "eligibility": "everyone", "startTime": "2025-05-29T19:26:48.726Z", "endTime": "2025-05-29T19:31:48.726Z", "timeRemaining": "5:00", "isPaused": false, "entries": [], "winners": []}, {"id": "EfBYFOlMLYudFddpz_Zyy", "title": "Game Launch Party", "prize": "Game Key", "date": "2023-05-10", "entriesCount": 212, "winner": "GamerFan789"}, {"id": "Jq9jL-RNC0vgxa0u989Fo", "title": "Monthly Subscriber Giveaway", "prize": "Gaming Headset", "date": "2023-05-28", "entriesCount": 87, "winner": "SubUser456"}, {"id": "ZrtLqXXil7_Eh5p03qWC7", "title": "1000 Followers Celebration", "prize": "Steam Gift Card ($20)", "date": "2023-06-15", "entriesCount": 145, "winner": "ViewerName123"}]}, "songs": {"queue": [], "current": null, "history": [{"id": "KBiuBACLKG2tzqRjANuAM", "title": "Sigma - <PERSON> To Love (Official Video) HD", "thumbnail": "https://i.ytimg.com/vi/KD5fLb-WgBU/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Af4JgALQBYoCDAgAEAEYRSBZKHIwDw==&rs=AOn4CLDtGkI652lzkSbc2Ap649IARAsxWw", "duration": 243, "requestedBy": "You", "source": "youtube", "sourceId": "KD5fLb-WgBU", "url": "https://www.youtube.com/watch?v=KD5fLb-WgBU", "currentTime": 243}, {"id": "KR8pWSgVmqhNtI_7lczT2", "title": "Kanye West - Bound 2", "thumbnail": "https://i.ytimg.com/vi/BBAtAM7vtgc/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA3P3_RlX8HRbmG4XHsHawE2WPxZg", "duration": 254, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "BBAtAM7vtgc", "url": "https://www.youtube.com/watch?v=BBAtAM7vtgc", "currentTime": 254}, {"id": "rLcgKbMvyl_Fpd6B9pzN3", "title": "S3RL - Shoulder Boulders HQ", "thumbnail": "https://i.ytimg.com/vi/jofBpB0OkcY/hq2.jpg?sqp=-oaymwE9COADEI4CSFryq4qpAy8IARUAAAAAGAElAADIQj0AgKJDeAHwAQH4Af4JgALQBYoCDAgAEAEYRyBJKFkwDw==&rs=AOn4CLAu4oMmErthtgWxTY1vQf1xoXEMsw", "duration": 270, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "jofBpB0OkcY", "url": "https://www.youtube.com/watch?v=jofBpB0OkcY", "currentTime": 270}, {"id": "1ugdDAvg10zud6T3XpRJT", "title": "10 Second Timer", "thumbnail": "https://i.ytimg.com/vi/tCDvOQI3pco/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA2SFWW7RQD1hTxQ6GAWHdbUWlL_g", "duration": 11, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "tCDvOQI3pco", "url": "https://www.youtube.com/watch?v=tCDvOQI3pco", "currentTime": 11}, {"id": "_wlQNFrd79Jnb6wwiItxa", "title": "S3RL - Shoulder Boulders HQ", "thumbnail": "https://i.ytimg.com/vi/jofBpB0OkcY/hq2.jpg?sqp=-oaymwE9COADEI4CSFryq4qpAy8IARUAAAAAGAElAADIQj0AgKJDeAHwAQH4Af4JgALQBYoCDAgAEAEYRyBJKFkwDw==&rs=AOn4CLAu4oMmErthtgWxTY1vQf1xoXEMsw", "duration": 270, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "jofBpB0OkcY", "url": "https://www.youtube.com/watch?v=jofBpB0OkcY", "currentTime": 6}, {"id": "DQshXLei3EmEtydEdJkrp", "title": "S3RL - Shoulder Boulders HQ", "thumbnail": "https://i.ytimg.com/vi/jofBpB0OkcY/hq2.jpg?sqp=-oaymwE9COADEI4CSFryq4qpAy8IARUAAAAAGAElAADIQj0AgKJDeAHwAQH4Af4JgALQBYoCDAgAEAEYRyBJKFkwDw==&rs=AOn4CLAu4oMmErthtgWxTY1vQf1xoXEMsw", "duration": 270, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "jofBpB0OkcY", "url": "https://www.youtube.com/watch?v=jofBpB0OkcY", "currentTime": 270}, {"id": "ca_jjvho8A4bT5p_lM2LO", "title": "<PERSON><PERSON><PERSON> - Runaway (Video Version) ft. <PERSON><PERSON><PERSON> <PERSON>", "thumbnail": "https://i.ytimg.com/vi/Bm5iA4Zupek/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLCewScOaSAdJXD7qkrfMin4dqj_HA", "duration": 278, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "Bm5iA4Zupek", "url": "https://www.youtube.com/watch?v=Bm5iA4Zupek", "currentTime": 112}, {"id": "L2YJFfoMo2EFLnnGv0GR4", "title": "DEV - <PERSON> Down Low (Explicit) ft. The Cataracs (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/OOAMfUJ3tsc/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLBhD9tyg7gqT0nk62f88vZ1j3Fi3A", "duration": 219, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "OOAMfUJ3tsc", "url": "https://www.youtube.com/watch?v=OOAMfUJ3tsc", "currentTime": 219}, {"id": "X4OuzNkh7C5pAYRcdM3vn", "title": "DEV - <PERSON> Down Low (Explicit) ft. The Cataracs (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/OOAMfUJ3tsc/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLBhD9tyg7gqT0nk62f88vZ1j3Fi3A", "duration": 219, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "OOAMfUJ3tsc", "url": "https://www.youtube.com/watch?v=OOAMfUJ3tsc", "currentTime": 42}, {"id": "Kl8ZH5ZxBw-HQLkBFQKxS", "title": "DEV - <PERSON> Down Low (Explicit) ft. The Cataracs (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/OOAMfUJ3tsc/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLBhD9tyg7gqT0nk62f88vZ1j3Fi3A", "duration": 219, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "OOAMfUJ3tsc", "url": "https://www.youtube.com/watch?v=OOAMfUJ3tsc", "currentTime": 3}, {"id": "hZiHEqm_iXais5C-3-Cr3", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 9}, {"id": "qEzm4jXWpjKdvj_GougoG", "title": "10 Second Timer", "thumbnail": "https://i.ytimg.com/vi/tCDvOQI3pco/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA2SFWW7RQD1hTxQ6GAWHdbUWlL_g", "duration": 11, "requestedBy": "You", "source": "youtube", "sourceId": "tCDvOQI3pco", "url": "https://www.youtube.com/watch?v=tCDvOQI3pco", "currentTime": 11}, {"id": "DHNkFaYKg4TquaTgU7alD", "title": "10 Second Timer", "thumbnail": "https://i.ytimg.com/vi/tCDvOQI3pco/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA2SFWW7RQD1hTxQ6GAWHdbUWlL_g", "duration": 11, "requestedBy": "You", "source": "youtube", "sourceId": "tCDvOQI3pco", "url": "https://www.youtube.com/watch?v=tCDvOQI3pco", "currentTime": 11}, {"id": "esOiUkvuxS8IJtrr2-KN7", "title": "10 Second Timer", "thumbnail": "https://i.ytimg.com/vi/tCDvOQI3pco/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA2SFWW7RQD1hTxQ6GAWHdbUWlL_g", "duration": 11, "requestedBy": "You", "source": "youtube", "sourceId": "tCDvOQI3pco", "url": "https://www.youtube.com/watch?v=tCDvOQI3pco", "currentTime": 11}, {"id": "nEQ3hBYiyxY_uepFBLCUA", "title": "10 Second Timer", "thumbnail": "https://i.ytimg.com/vi/tCDvOQI3pco/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA2SFWW7RQD1hTxQ6GAWHdbUWlL_g", "duration": 11, "requestedBy": "You", "source": "youtube", "sourceId": "tCDvOQI3pco", "url": "https://www.youtube.com/watch?v=tCDvOQI3pco", "currentTime": 11}, {"id": "ZUKm4_q_7i8SHEdHGO6Z9", "title": "10 Second Timer", "thumbnail": "https://i.ytimg.com/vi/tCDvOQI3pco/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA2SFWW7RQD1hTxQ6GAWHdbUWlL_g", "duration": 11, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "tCDvOQI3pco", "url": "https://www.youtube.com/watch?v=tCDvOQI3pco", "currentTime": 11}, {"id": "6Vp1A4dI_-_lGQsqOxFEr", "title": "10 Second Timer", "thumbnail": "https://i.ytimg.com/vi/tCDvOQI3pco/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA2SFWW7RQD1hTxQ6GAWHdbUWlL_g", "duration": 11, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "tCDvOQI3pco", "url": "https://www.youtube.com/watch?v=tCDvOQI3pco", "currentTime": 11}, {"id": "WVYTWrd_8FxpGHVGNJZxa", "title": "10 Second Timer", "thumbnail": "https://i.ytimg.com/vi/tCDvOQI3pco/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA2SFWW7RQD1hTxQ6GAWHdbUWlL_g", "duration": 11, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "tCDvOQI3pco", "url": "https://www.youtube.com/watch?v=tCDvOQI3pco", "currentTime": 11}, {"id": "W_mGPSwQVS2rcVA-thfY1", "title": "10 Second Timer", "thumbnail": "https://i.ytimg.com/vi/tCDvOQI3pco/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLA2SFWW7RQD1hTxQ6GAWHdbUWlL_g", "duration": 11, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "tCDvOQI3pco", "url": "https://www.youtube.com/watch?v=tCDvOQI3pco", "currentTime": 11}, {"id": "ezhm2drG4RB44o9wWhoeP", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 138}, {"id": "SSW24ixq5fTLsaWJhxUxD", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 138}, {"id": "pPxbvEgRgEbuaUAJii1cn", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 92}, {"id": "zRwJ5n4UjDfRXQ0kuON3i", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 138}, {"id": "lT0dnz99wHZa6GkFE-s8Z", "title": "DEV - <PERSON> Down Low (Explicit) ft. The Cataracs (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/OOAMfUJ3tsc/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLBhD9tyg7gqT0nk62f88vZ1j3Fi3A", "duration": 219, "requestedBy": "You", "source": "youtube", "sourceId": "OOAMfUJ3tsc", "url": "https://www.youtube.com/watch?v=OOAMfUJ3tsc", "currentTime": 0}, {"id": "FkyHFdWyPiBZ40u-HPt31", "title": "DEV - <PERSON> Down Low (Explicit) ft. The Cataracs (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/OOAMfUJ3tsc/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLBhD9tyg7gqT0nk62f88vZ1j3Fi3A", "duration": 219, "requestedBy": "You", "source": "youtube", "sourceId": "OOAMfUJ3tsc", "url": "https://www.youtube.com/watch?v=OOAMfUJ3tsc", "currentTime": 218}, {"id": "_1gMVKIKqa9azd9N3Kc9e", "title": "DEV - <PERSON> Down Low (Explicit) ft. The Cataracs (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/OOAMfUJ3tsc/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLBhD9tyg7gqT0nk62f88vZ1j3Fi3A", "duration": 219, "requestedBy": "You", "source": "youtube", "sourceId": "OOAMfUJ3tsc", "url": "https://www.youtube.com/watch?v=OOAMfUJ3tsc", "currentTime": 219}, {"id": "XxxqqkDJEquOs9jYOI3ub", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 9}, {"id": "DjEZSo3C669CGCEyM_aGl", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 2}, {"id": "13HO-jceUEUCnJLytEOhU", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 5}, {"id": "HjZo6hAgMsrBHI0ausyOp", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 81}, {"id": "YFbbQPVdlFm4JyfSI1w_Y", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 4}, {"id": "P2xMIpm1b7CcDjHdCdSxA", "title": "b-b-BASS DOWN LOW (Official Audio)", "thumbnail": "https://i.ytimg.com/vi/CE2F3m_6HhE/hq720.jpg?sqp=-oaymwExCNAFEJQDSFryq4qpAyMIARUAAIhCGAHwAQH4Ac4FgALQBYoCDAgAEAEYHiAzKH8wDw==&rs=AOn4CLCxF3kP3Mb6SQO4vFzt5IJmS3XJpg", "duration": 138, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "CE2F3m_6HhE", "url": "https://www.youtube.com/watch?v=CE2F3m_6HhE", "currentTime": 138}, {"id": "OIXloDe5_fBlDlF5d10__", "title": "In The End [Official HD Music Video] - Linkin Park", "thumbnail": "https://i.ytimg.com/vi/eVTXPUF4Oz4/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAH0lsAjGP90PQpm0PzK_SPvTLXjA", "duration": 219, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "eVTXPUF4Oz4", "url": "https://www.youtube.com/watch?v=eVTXPUF4Oz4", "currentTime": 219}, {"id": "J9RxR_HexXNYxr71n86E3", "title": "<PERSON><PERSON><PERSON> - Pink Pony Club (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/GR3Liudev18/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAclxh9_HxpN8kY2ofRk4VIRfIdQg", "duration": 281, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "GR3Liudev18", "url": "https://www.youtube.com/watch?v=GR3Liudev18", "currentTime": 11}, {"id": "-vKvBYQ2YlJa1mf9LEUXy", "title": "<PERSON><PERSON><PERSON> - Pink Pony Club (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/GR3Liudev18/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAclxh9_HxpN8kY2ofRk4VIRfIdQg", "duration": 281, "requestedBy": "You", "source": "youtube", "sourceId": "GR3Liudev18", "url": "https://www.youtube.com/watch?v=GR3Liudev18", "currentTime": 42}, {"id": "lVg98_yPU07heRqrxCiTj", "title": "<PERSON><PERSON><PERSON> - Pink Pony Club (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/GR3Liudev18/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAclxh9_HxpN8kY2ofRk4VIRfIdQg", "duration": 281, "requestedBy": "You", "source": "youtube", "sourceId": "GR3Liudev18", "url": "https://www.youtube.com/watch?v=GR3Liudev18", "currentTime": 95}, {"id": "hATwQ5KXtXoaExYW2D3sL", "title": "<PERSON><PERSON><PERSON> - Pink Pony Club (Official Music Video)", "thumbnail": "https://i.ytimg.com/vi/GR3Liudev18/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAclxh9_HxpN8kY2ofRk4VIRfIdQg", "duration": 281, "requestedBy": "You", "source": "youtube", "sourceId": "GR3Liudev18", "url": "https://www.youtube.com/watch?v=GR3Liudev18", "currentTime": 281}, {"id": "9eywKMc9xcseyDD5bW4li", "title": "In The End [Official HD Music Video] - Linkin Park", "thumbnail": "https://i.ytimg.com/vi/eVTXPUF4Oz4/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAH0lsAjGP90PQpm0PzK_SPvTLXjA", "duration": 219, "requestedBy": "You", "source": "youtube", "sourceId": "eVTXPUF4Oz4", "url": "https://www.youtube.com/watch?v=eVTXPUF4Oz4", "currentTime": 219}, {"id": "O7ou5kfsVkErcAIgCK0HO", "title": "Wheatus - Teenage Dirtbag (Official Video)", "thumbnail": "https://i.ytimg.com/vi/FC3y9llDXuM/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAoLswOVZHO56ZTOLmWXfL1kiahZQ", "duration": 247, "requestedBy": "You", "source": "youtube", "sourceId": "FC3y9llDXuM", "url": "https://www.youtube.com/watch?v=FC3y9llDXuM", "currentTime": 156}, {"id": "qa_TJ9ASB2qV-XikioS19", "title": "KATAMARI", "thumbnail": "https://i.ytimg.com/vi/kky1L2jzc8c/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLDY3J6m-4R63gM5DK9eCs9_5fQIjQ", "duration": 159, "requestedBy": "You", "source": "youtube", "sourceId": "kky1L2jzc8c", "url": "https://www.youtube.com/watch?v=kky1L2jzc8c", "currentTime": 159}, {"id": "dS97up7CUbIbuTNjb73Mk", "title": "KATAMARI", "thumbnail": "https://i.ytimg.com/vi/kky1L2jzc8c/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLDY3J6m-4R63gM5DK9eCs9_5fQIjQ", "duration": 159, "requestedBy": "You", "source": "youtube", "sourceId": "kky1L2jzc8c", "url": "https://www.youtube.com/watch?v=kky1L2jzc8c", "currentTime": 38}, {"id": "uvkBqkkG16iCVjf2SBilg", "title": "KATAMARI", "thumbnail": "https://i.ytimg.com/vi/kky1L2jzc8c/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLDY3J6m-4R63gM5DK9eCs9_5fQIjQ", "duration": 159, "requestedBy": "You", "source": "youtube", "sourceId": "kky1L2jzc8c", "url": "https://www.youtube.com/watch?v=kky1L2jzc8c", "currentTime": 100}, {"id": "018cjhHt8IDQqTGJ9IZln", "title": "femtanyl - KATAMARI", "thumbnail": "https://i.ytimg.com/vi/YfXpNIQbdjo/hqdefault.jpg?sqp=-oaymwE9COADEI4CSFryq4qpAy8IARUAAAAAGAElAADIQj0AgKJDeAHwAQH4AY4FgALgA4oCDAgAEAEYQyBMKGUwDw==&rs=AOn4CLBbATfVuhES0KnVZBEjxFBRp8xzdQ", "duration": 159, "requestedBy": "You", "source": "youtube", "sourceId": "YfXpNIQbdjo", "url": "https://www.youtube.com/watch?v=YfXpNIQbdjo", "currentTime": 159}, {"id": "oZg5nBLfrBmaKhy51OpnY", "title": "In The End [Official HD Music Video] - Linkin Park", "thumbnail": "https://i.ytimg.com/vi/eVTXPUF4Oz4/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAH0lsAjGP90PQpm0PzK_SPvTLXjA", "duration": 219, "requestedBy": "You", "source": "youtube", "sourceId": "eVTXPUF4Oz4", "url": "https://www.youtube.com/watch?v=eVTXPUF4Oz4", "currentTime": 124}, {"id": "oru1thnr3_P6B43F8FkK3", "title": "In The End [Official HD Music Video] - Linkin Park", "thumbnail": "https://i.ytimg.com/vi/eVTXPUF4Oz4/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAH0lsAjGP90PQpm0PzK_SPvTLXjA", "duration": 219, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "eVTXPUF4Oz4", "url": "https://www.youtube.com/watch?v=eVTXPUF4Oz4", "currentTime": 89}, {"id": "xUsJRf3y0rN1IKbzMvC4a", "title": "In The End [Official HD Music Video] - Linkin Park", "thumbnail": "https://i.ytimg.com/vi/eVTXPUF4Oz4/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLAH0lsAjGP90PQpm0PzK_SPvTLXjA", "duration": 219, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "eVTXPUF4Oz4", "url": "https://www.youtube.com/watch?v=eVTXPUF4Oz4", "currentTime": 119}, {"id": "QIpQFry-24_2oxVnr8JzK", "title": "<PERSON><PERSON><PERSON> - ...Baby One More Time (Official Video)", "thumbnail": "https://i.ytimg.com/vi/C-u5WLJ9Yk4/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLCaGB6xutLVi67kxXjXT9lVHs1-VQ", "duration": 237, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "C-u5WLJ9Yk4", "url": "https://www.youtube.com/watch?v=C-u5WLJ9Yk4", "currentTime": 233}, {"id": "q9KV5GjYktQyIvwajT9t2", "title": "<PERSON> & OneRepublic - I Don't Wanna Wait (Official Video)", "thumbnail": "https://i.ytimg.com/vi/dSDbwfXX5_I/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLD-vGuRk03xCMh0VtG3kGWJSwL5eA", "duration": 155, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "dSDbwfXX5_I", "url": "https://www.youtube.com/watch?v=dSDbwfXX5_I", "currentTime": 6}, {"id": "ZiFrndoe22HltbTwvvKds", "title": "femtanyl - KATAMARI", "thumbnail": "https://i.ytimg.com/vi/YfXpNIQbdjo/hqdefault.jpg?sqp=-oaymwE9COADEI4CSFryq4qpAy8IARUAAAAAGAElAADIQj0AgKJDeAHwAQH4AY4FgALgA4oCDAgAEAEYQyBMKGUwDw==&rs=AOn4CLBbATfVuhES0KnVZBEjxFBRp8xzdQ", "duration": 159, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "YfXpNIQbdjo", "url": "https://www.youtube.com/watch?v=YfXpNIQbdjo", "currentTime": 13}, {"id": "Yv6qTUn6PzRQYG0TxNTao", "title": "<PERSON><PERSON><PERSON> - Runaway (Video Version) ft. <PERSON><PERSON><PERSON> <PERSON>", "thumbnail": "https://i.ytimg.com/vi/Bm5iA4Zupek/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLCewScOaSAdJXD7qkrfMin4dqj_HA", "duration": 278, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "Bm5iA4Zupek", "url": "https://www.youtube.com/watch?v=Bm5iA4Zupek", "currentTime": 32}, {"id": "oa4NES7OiyMdkbj6HLso7", "title": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "thumbnail": "https://i.ytimg.com/vi/Co0tTeuUVhU/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLB6dcROje4APTSCbKo6tE5k_FlueQ", "duration": 221, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "Co0tTeuUVhU", "url": "https://www.youtube.com/watch?v=Co0tTeuUVhU", "currentTime": 213}, {"id": "S80NnqSMMY2V68cbvClvD", "title": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "artist": "Unknown Channel", "thumbnail": "https://i.ytimg.com/vi/Co0tTeuUVhU/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLB6dcROje4APTSCbKo6tE5k_FlueQ", "duration": 221, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "Co0tTeuUVhU", "url": "https://www.youtube.com/watch?v=Co0tTeuUVhU", "currentTime": 221}, {"id": "N6uHC-T_hCQRZVW1MS-g4", "title": "KATAMARI", "artist": "Unknown Channel", "thumbnail": "https://i.ytimg.com/vi/kky1L2jzc8c/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLDY3J6m-4R63gM5DK9eCs9_5fQIjQ", "duration": 159, "requestedBy": "You", "source": "youtube", "sourceId": "kky1L2jzc8c", "url": "https://www.youtube.com/watch?v=kky1L2jzc8c", "currentTime": 75}, {"id": "jS5bBYEErZWz8i8f6TTzO", "title": "<PERSON><PERSON><PERSON> - <PERSON><PERSON>", "artist": "Unknown Channel", "thumbnail": "https://i.ytimg.com/vi/Co0tTeuUVhU/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLB6dcROje4APTSCbKo6tE5k_FlueQ", "duration": 221, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "Co0tTeuUVhU", "url": "https://www.youtube.com/watch?v=Co0tTeuUVhU", "currentTime": 0}, {"id": "VJPQA6iciGhJCwYAjeu3B", "title": "KATAMARI", "artist": "Unknown Channel", "thumbnail": "https://i.ytimg.com/vi/kky1L2jzc8c/hq720.jpg?sqp=-oaymwEXCNAFEJQDSFryq4qpAwkIARUAAIhCGAE=&rs=AOn4CLDY3J6m-4R63gM5DK9eCs9_5fQIjQ", "duration": 159, "requestedBy": "You", "source": "youtube", "sourceId": "kky1L2jzc8c", "url": "https://www.youtube.com/watch?v=kky1L2jzc8c", "currentTime": 0}, {"id": "V-EIOcAdRiDNZZgLS__qr", "title": "katamari femtanyl - Official Video", "artist": "Various Artists", "thumbnail": "https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg", "duration": 212, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "", "url": "https://www.youtube.com/results?search_query=katamari%20femtanyl", "currentTime": 0}, {"id": "HmCQEL6N7T8xt10kA7EHL", "title": "bang ajr - Official Video", "artist": "Various Artists", "thumbnail": "https://i.ytimg.com/vi/dQw4w9WgXcQ/mqdefault.jpg", "duration": 212, "requestedBy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "source": "youtube", "sourceId": "", "url": "https://www.youtube.com/results?search_query=bang%20ajr", "currentTime": 212}]}, "chat": {"messages": [{"id": "y2VyN2-ZJSQoKQ-KCAOop", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "ballsack", "color": "#48E848", "timestamp": 1748544725577, "isMod": false, "isSubscriber": false}, {"id": "nJ3pzQcCYJM8qoiwMPDTp", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "ball", "color": "#48E848", "timestamp": 1748545604864, "isMod": false, "isSubscriber": false}, {"id": "p7SJKnThxCdsdhZylQcXP", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "dog", "color": "#48E848", "timestamp": 1748546784339, "isMod": false, "isSubscriber": false}, {"id": "_kKC_N-eDeICeBCKZBc-j", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546790867, "isMod": false, "isSubscriber": false}, {"id": "b0tKC0rwh_KbIZMZ1EumI", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546792432, "isMod": false, "isSubscriber": false}, {"id": "nHFCRBSZozHj3YQ0UObjZ", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546792611, "isMod": false, "isSubscriber": false}, {"id": "KBR073YOYFRAxCuAffpi0", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546792809, "isMod": false, "isSubscriber": false}, {"id": "UL-YXED1JzkbfdHvxzntB", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546792954, "isMod": false, "isSubscriber": false}, {"id": "1_4TrN8W59IbLs5qp_5-1", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gay", "color": "#48E848", "timestamp": 1748546793018, "isMod": false, "isSubscriber": false}, {"id": "EJKhC2IUW9JqgrrtTRWZE", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "gw", "color": "#48E848", "timestamp": 1748546811331, "isMod": false, "isSubscriber": false}, {"id": "F8XcZyyFm_9xdzVIg3jSK", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!discord", "color": "#48E848", "timestamp": 1748546841644, "isMod": false, "isSubscriber": false}, {"id": "WbeGudRbvGZ9ec9gUzJDE", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!!toast", "color": "#48E848", "timestamp": 1748546855669, "isMod": false, "isSubscriber": false}, {"id": "T9xWWRSHeoH4yhOdDokd7", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748547231502, "isMod": false, "isSubscriber": false}, {"id": "APaWXPYQKqbgq2GCbgK84", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748547882428, "isMod": false, "isSubscriber": false}, {"id": "nz3N269SAzdmscc6Bk5RL", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr my balls", "color": "#48E848", "timestamp": 1748547994065, "isMod": false, "isSubscriber": false}, {"id": "EKwvXX2Wybc_6WIT15OMB", "username": "nightbot", "message": "@lordwolfyyy -> \"Your Favorite Martian - My Balls [Official Music Video]\" by Your Favorite Martian has been added to the queue in position #2", "color": "#7C7CE1", "timestamp": 1748547994766, "isMod": true, "isSubscriber": false}, {"id": "NlQCbG1vRMvcMa9GxqSyt", "username": "streamelements", "message": "@lordwolfyyy, added Your Favorite Martian - \"Your Favorite Martian - My Balls [Official Music Video]\" to the queue at #4 (playing ~in  10 mins 39 secs) https://youtu.be/6g65f3FbVRY", "color": "#5B99FF", "timestamp": 1748547994820, "isMod": true, "isSubscriber": false}, {"id": "549couz3BI84dj_IWTPrg", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr gay", "color": "#48E848", "timestamp": 1748547996792, "isMod": false, "isSubscriber": false}, {"id": "567mrsqVkoRyN2gDncTWG", "username": "streamelements", "message": "@lordwolfyyy, added al jokes - \"gay vs GAY #shorts #comedy #funny\" to the queue at #5 (playing ~in  13 mins 41 secs) https://youtu.be/PUcH7PruuiA", "color": "#5B99FF", "timestamp": 1748547997512, "isMod": true, "isSubscriber": false}, {"id": "16TdT-rtZ_Ndk76uXNv6s", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr sex", "color": "#48E848", "timestamp": 1748547999735, "isMod": false, "isSubscriber": false}, {"id": "NX-SNA8dbF7SwVNSXCrY4", "username": "nightbot", "message": "@lordwolfyyy -> \"BUENSA – Sex In The City ft. Gat Putch & FRNC$ (Official Music Video)\" by BUENSA has been added to the queue in position #3", "color": "#7C7CE1", "timestamp": 1748548000375, "isMod": true, "isSubscriber": false}, {"id": "JbFq45i-Bs6EdF9SihGyq", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON>clips - \"Black Snake Moan (2007) - Sex Withdrawals Scene | Movieclips\" to the queue at #6 (playing ~in  14 mins) https://youtu.be/ucZbcz6MWws", "color": "#5B99FF", "timestamp": 1748548000403, "isMod": true, "isSubscriber": false}, {"id": "ocw9Snr9HMB2LeCOm9pFx", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr my balls", "color": "#48E848", "timestamp": 1748548001331, "isMod": false, "isSubscriber": false}, {"id": "INVtQWM6SO9XlZSEOhrMe", "username": "streamelements", "message": "@lordwolfyyy, added Your Favorite Martian - \"Your Favorite Martian - My Balls [Official Music Video]\" to the queue at #4 (playing ~in  10 mins 39 secs) https://youtu.be/6g65f3FbVRY", "color": "#5B99FF", "timestamp": 1748548001988, "isMod": true, "isSubscriber": false}, {"id": "_9XB0JoozKgEzYT33Wz9D", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr popular the weekend", "color": "#48E848", "timestamp": 1748548565718, "isMod": false, "isSubscriber": false}, {"id": "lL38-_Vc7mF5DhPoD2Sz1", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748548566259, "isMod": true, "isSubscriber": false}, {"id": "GUwNhJzO_eE_CG83UsBHV", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON>Weeknd<PERSON><PERSON> - \"<PERSON> Weeknd, <PERSON>, <PERSON><PERSON><PERSON> - <PERSON> (Official Music Video)\" to the queue at #8 (playing ~in  19 mins 50 secs) https://youtu.be/vt0i6nuqNEo", "color": "#5B99FF", "timestamp": 1748548566408, "isMod": true, "isSubscriber": false}, {"id": "80hIU0k5fTHahTuz71Qfc", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr bang ajr", "color": "#48E848", "timestamp": 1748549108509, "isMod": false, "isSubscriber": false}, {"id": "OBHrFLNsoCQcL6JWstoWr", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748549108967, "isMod": true, "isSubscriber": false}, {"id": "YXKYQOooHvwPmSdMAyFkJ", "username": "streamelements", "message": "@lordwolfyyy, added 7clouds - \"AJR - BANG! (Lyrics)\" to the queue at #9 (playing ~in  23 mins 41 secs) https://youtu.be/9e2buqBpSBU", "color": "#5B99FF", "timestamp": 1748549109230, "isMod": true, "isSubscriber": false}, {"id": "5DgArO2OATgbA1eDtb2wM", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr katamari femtanyl", "color": "#48E848", "timestamp": 1748550069418, "isMod": false, "isSubscriber": false}, {"id": "HfsNC1TyljFxHTgZdLEBT", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748550069972, "isMod": true, "isSubscriber": false}, {"id": "pdO4QWTBj90m-XBe2Npvc", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON><PERSON><PERSON> - \"femtanyl - KATAMARI\" to the queue at #10 (playing ~in  26 mins 32 secs) https://youtu.be/YfXpNIQbdjo", "color": "#5B99FF", "timestamp": 1748550070110, "isMod": true, "isSubscriber": false}, {"id": "dkxmQnHlvGDtwwkj0RF7h", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr heartless - kanye west", "color": "#48E848", "timestamp": 1748550613771, "isMod": false, "isSubscriber": false}, {"id": "7k34hzVZkOi3l5BM-Lb9i", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748550614322, "isMod": true, "isSubscriber": false}, {"id": "oCDhv1Taiqn8fohktbBaz", "username": "streamelements", "message": "@lordwolfyyy, added Lyrical Tracks 🎵 - \"<PERSON><PERSON><PERSON> (Lyrics)\" to the queue at #11 (playing ~in  29 mins 11 secs) https://youtu.be/HlWISmjCfb8", "color": "#5B99FF", "timestamp": 1748550614496, "isMod": true, "isSubscriber": false}, {"id": "g636pNx9I2ZHI_tgCOB4a", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr <PERSON><PERSON>e west - heartless", "color": "#48E848", "timestamp": 1748560480235, "isMod": false, "isSubscriber": false}, {"id": "29azrl2F63EH-9B5dhBFj", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748560480846, "isMod": true, "isSubscriber": false}, {"id": "sn1ZMh8HYvDooFiPgvKDf", "username": "streamelements", "message": "@lordwolfyyy, added Lyrical Tracks 🎵 - \"<PERSON><PERSON><PERSON> (Lyrics)\" to the queue at #11 (playing ~in  29 mins 11 secs) https://youtu.be/HlWISmjCfb8", "color": "#5B99FF", "timestamp": 1748560480944, "isMod": true, "isSubscriber": false}, {"id": "Ca14m_XBM5vZZFFXRI7C9", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr Runanway - kanye west", "color": "#48E848", "timestamp": 1748600601179, "isMod": false, "isSubscriber": false}, {"id": "FN_nL8dvWgS3WGBXD9rjA", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748600601738, "isMod": true, "isSubscriber": false}, {"id": "xvWrRZ4XzB1jm-Fpp47vs", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON> - \"<PERSON><PERSON><PERSON> (Lyrics) ft. <PERSON><PERSON><PERSON> T\" to the queue at #13 (playing ~in  36 mins 11 secs) https://youtu.be/4TVT7IOqH1Y", "color": "#5B99FF", "timestamp": 1748600601913, "isMod": true, "isSubscriber": false}, {"id": "E5rwCQJHe15DlBG9kzrfj", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr femtanyl - katamari", "color": "#48E848", "timestamp": 1748600838498, "isMod": false, "isSubscriber": false}, {"id": "Mg7ZTbWE7rbDYBRuNEeQT", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748600838991, "isMod": true, "isSubscriber": false}, {"id": "kL78NILkNnMu-PcQ2_EZ8", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON><PERSON><PERSON> - \"femtanyl - KATAMARI\" to the queue at #10 (playing ~in  26 mins 32 secs) https://youtu.be/YfXpNIQbdjo", "color": "#5B99FF", "timestamp": 1748600839066, "isMod": true, "isSubscriber": false}, {"id": "0k7nMcxGWLe-k0zLF1sw9", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr i dont wanta wait", "color": "#48E848", "timestamp": 1748600847126, "isMod": false, "isSubscriber": false}, {"id": "wKuX1I_Y-ENWuZf-tEryy", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748600847621, "isMod": true, "isSubscriber": false}, {"id": "cE2dXoPB8CpGZ5ZLmEHTm", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON> - \"<PERSON> & OneRepublic - I Don't Wanna Wait (Official Video)\" to the queue at #15 (playing ~in  44 mins 28 secs) https://youtu.be/dSDbwfXX5_I", "color": "#5B99FF", "timestamp": 1748600847728, "isMod": true, "isSubscriber": false}, {"id": "KocBfGQpr6HXkUMqduVa3", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr hit me baby one more time", "color": "#48E848", "timestamp": 1748601952465, "isMod": false, "isSubscriber": false}, {"id": "EbnphGGkNNNU22bxsURlM", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748601953031, "isMod": true, "isSubscriber": false}, {"id": "VswOBXkdRHojMPLodE2Rp", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - \"Brit<PERSON> Spears - ...Baby One More Time (Official Video)\" to the queue at #16 (playing ~in  47 mins 3 secs) https://youtu.be/C-u5WLJ9Yk4", "color": "#5B99FF", "timestamp": 1748601953132, "isMod": true, "isSubscriber": false}, {"id": "Ut2sCKevQQkZL8zFT61V3", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr in the end", "color": "#48E848", "timestamp": 1748602195442, "isMod": false, "isSubscriber": false}, {"id": "eMaWdzD5qawtnVO4JUlS9", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748602195872, "isMod": true, "isSubscriber": false}, {"id": "7lZ00z_wGP6q-v6COjXrT", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON> - \"In The End [Official HD Music Video] - Linkin Park\" to the queue at #17 (playing ~in  51 mins) https://youtu.be/eVTXPUF4Oz4", "color": "#5B99FF", "timestamp": 1748602196160, "isMod": true, "isSubscriber": false}, {"id": "yXxme6-m4w434ladbyWkV", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr in the end", "color": "#48E848", "timestamp": 1748602344445, "isMod": false, "isSubscriber": false}, {"id": "cX3_hOrPODxEZBhWYDd3A", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748602345049, "isMod": true, "isSubscriber": false}, {"id": "zLRVM4ohDC4T-O7G3qFkn", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON> - \"In The End [Official HD Music Video] - Linkin Park\" to the queue at #17 (playing ~in  51 mins) https://youtu.be/eVTXPUF4Oz4", "color": "#5B99FF", "timestamp": 1748602345102, "isMod": true, "isSubscriber": false}, {"id": "0LYx0XtlOF_hw7trmprfX", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748604375623, "isMod": true, "isSubscriber": false}, {"id": "P9sFWhrCssU9qVmc97C1x", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - \"Chappell Roan - Pink Pony Club (Official Music Video)\" to the queue at #19 (playing ~in  58 mins 18 secs) https://youtu.be/GR3Liudev18", "color": "#5B99FF", "timestamp": 1748604375738, "isMod": true, "isSubscriber": false}, {"id": "Vv_Z3ZTlPMXLURvhUd6Qe", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr pink pony club", "color": "#48E848", "timestamp": 1748604403514, "isMod": false, "isSubscriber": false}, {"id": "69JgfJ4Lygd_wjjin-Yfn", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748604403925, "isMod": true, "isSubscriber": false}, {"id": "vk9oNyJP6bcSnt8JEqUQ3", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - \"Chappell Roan - Pink Pony Club (Official Music Video)\" to the queue at #19 (playing ~in  58 mins 18 secs) https://youtu.be/GR3Liudev18", "color": "#5B99FF", "timestamp": 1748604404125, "isMod": true, "isSubscriber": false}, {"id": "b4h2RP2X3MacTVDNEw-xK", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr in the end", "color": "#48E848", "timestamp": 1748604426841, "isMod": false, "isSubscriber": false}, {"id": "E9FIsCnbvSMTa7UJfjPpp", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748604427384, "isMod": true, "isSubscriber": false}, {"id": "HOKRQv9Mz6nJ3KGdXCzyd", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON> - \"In The End [Official HD Music Video] - Linkin Park\" to the queue at #17 (playing ~in  51 mins) https://youtu.be/eVTXPUF4Oz4", "color": "#5B99FF", "timestamp": 1748604427546, "isMod": true, "isSubscriber": false}, {"id": "CkEJzOTf2EjRGTDi_paK3", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-b-ass down low russelbuck", "color": "#48E848", "timestamp": 1748604468369, "isMod": false, "isSubscriber": false}, {"id": "Cft8enRNyBjoB8f-nEb8r", "username": "streamelements", "message": "@lordwolfyyy, added r u s s e l b u c k - \"b-b-BASS DOWN LOW (Official Audio)\" to the queue at #22 (playing ~in  1 hour 11 mins) https://youtu.be/CE2F3m_6HhE", "color": "#5B99FF", "timestamp": 1748604468845, "isMod": true, "isSubscriber": false}, {"id": "EUPk7nJAKnMKyGHrXrI3W", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748604469130, "isMod": true, "isSubscriber": false}, {"id": "4uekuMNsI4RbFVT291VBK", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-b-ass down low russelbuck", "color": "#48E848", "timestamp": 1748604687111, "isMod": false, "isSubscriber": false}, {"id": "KcnFMChMSBgaL5rvdenSb", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748604687476, "isMod": true, "isSubscriber": false}, {"id": "_1O64S287rVVMvBu52gGg", "username": "streamelements", "message": "@lordwolfyyy, added r u s s e l b u c k - \"b-b-BASS DOWN LOW (Official Audio)\" to the queue at #22 (playing ~in  1 hour 11 mins) https://youtu.be/CE2F3m_6HhE", "color": "#5B99FF", "timestamp": 1748604687550, "isMod": true, "isSubscriber": false}, {"id": "mEBXcdr5OCt81IT26JDQz", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-b-ass down low russelbuck", "color": "#48E848", "timestamp": 1748604688090, "isMod": false, "isSubscriber": false}, {"id": "M_cLt2Oym1pcxREPRpFTq", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-b-ass down low russelbuck", "color": "#48E848", "timestamp": 1748604688349, "isMod": false, "isSubscriber": false}, {"id": "k1aVVfRm75prSBZ5_iTA2", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-b-ass down low russelbuck", "color": "#48E848", "timestamp": 1748604688922, "isMod": false, "isSubscriber": false}, {"id": "1gWlfnifQSzRLIoSNecF-", "username": "streamelements", "message": "@lordwolfyyy, added r u s s e l b u c k - \"b-b-BASS DOWN LOW (Official Audio)\" to the queue at #22 (playing ~in  1 hour 11 mins) https://youtu.be/CE2F3m_6HhE", "color": "#5B99FF", "timestamp": 1748604689416, "isMod": true, "isSubscriber": false}, {"id": "V0mEFjQnSTBk48bFcabvM", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-b-ass down low russelbuck", "color": "#48E848", "timestamp": 1748604884992, "isMod": false, "isSubscriber": false}, {"id": "Gokzk9kvssIaSSJyiNso_", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748604885401, "isMod": true, "isSubscriber": false}, {"id": "80Ij-Me8Y7QCCu4tJalwE", "username": "streamelements", "message": "@lordwolfyyy, added r u s s e l b u c k - \"b-b-BASS DOWN LOW (Official Audio)\" to the queue at #22 (playing ~in  1 hour 11 mins) https://youtu.be/CE2F3m_6HhE", "color": "#5B99FF", "timestamp": 1748604885574, "isMod": true, "isSubscriber": false}, {"id": "4w6aTLML-acS3jfZbSrKW", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-b-ass down low russelbuck", "color": "#48E848", "timestamp": 1748604935973, "isMod": false, "isSubscriber": false}, {"id": "mTYpxrED6RVhtr1f6-6AE", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748604936370, "isMod": true, "isSubscriber": false}, {"id": "gIhvOt-WqWDsuZl0SwrEB", "username": "streamelements", "message": "@lordwolfyyy, added r u s s e l b u c k - \"b-b-BASS DOWN LOW (Official Audio)\" to the queue at #22 (playing ~in  1 hour 11 mins) https://youtu.be/CE2F3m_6HhE", "color": "#5B99FF", "timestamp": 1748604936424, "isMod": true, "isSubscriber": false}, {"id": "JiMF5hlMGbsv4bSl6YlK7", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748605104899, "isMod": false, "isSubscriber": false}, {"id": "D0sgTssJ0o_dgPFTucZ3R", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748605135977, "isMod": false, "isSubscriber": false}, {"id": "SDq4jYjfC0u-pTAtugoEg", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-bass down low russelbuck", "color": "#48E848", "timestamp": 1748605914223, "isMod": false, "isSubscriber": false}, {"id": "wc7Eazqi0eoyaYCwMkaOh", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-bass down low russelbuck", "color": "#48E848", "timestamp": 1748605914431, "isMod": false, "isSubscriber": false}, {"id": "TEdJqc5bhv0C-5GJf96KH", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748605914614, "isMod": true, "isSubscriber": false}, {"id": "i-9wuhi0_VhrMKpDigIvb", "username": "streamelements", "message": "@lordwolfyyy, added r u s s e l b u c k - \"b-b-BASS DOWN LOW (Official Audio)\" to the queue at #22 (playing ~in  1 hour 11 mins) https://youtu.be/CE2F3m_6HhE", "color": "#5B99FF", "timestamp": 1748605914774, "isMod": true, "isSubscriber": false}, {"id": "3K6LOnA8NnW1RbWJwPwHa", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-bass down low russelbuck", "color": "#48E848", "timestamp": 1748606162089, "isMod": false, "isSubscriber": false}, {"id": "TOqrKaZk2WPo2MkaLBAe5", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-bass down low russelbuck", "color": "#48E848", "timestamp": 1748606162350, "isMod": false, "isSubscriber": false}, {"id": "i-5wQzAlFsdZCeTVBB2ah", "username": "streamelements", "message": "@lordwolfyyy, added r u s s e l b u c k - \"b-b-BASS DOWN LOW (Official Audio)\" to the queue at #22 (playing ~in  1 hour 11 mins) https://youtu.be/CE2F3m_6HhE", "color": "#5B99FF", "timestamp": 1748606162501, "isMod": true, "isSubscriber": false}, {"id": "_FLyu9ZolrF-g9lkhjomL", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748606162507, "isMod": true, "isSubscriber": false}, {"id": "fVjLBVZC7CWbtfYYlI3s5", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!join", "color": "#48E848", "timestamp": 1748606299168, "isMod": false, "isSubscriber": false}, {"id": "Q6krgtahhWhNiv8dDP5HT", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr 10s timer", "color": "#48E848", "timestamp": 1748606550903, "isMod": false, "isSubscriber": false}, {"id": "lpGBGQS0_sNhW_5E_Xd3d", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: video too long (10 min max)", "color": "#7C7CE1", "timestamp": 1748606551395, "isMod": true, "isSubscriber": false}, {"id": "yZl1vC2z0fWJ0tams7QLE", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON> - \"10 Second Timer\" to the queue at #29 (playing ~in  1 hour 27 mins) https://youtu.be/tCDvOQI3pco", "color": "#5B99FF", "timestamp": 1748606551518, "isMod": true, "isSubscriber": false}, {"id": "kGaSD34qMBvEiy3wS3fKE", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr 10s timer", "color": "#48E848", "timestamp": 1748606551654, "isMod": false, "isSubscriber": false}, {"id": "nG4Bdgxdz5fgsPa5X5EtL", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr 10 second timer", "color": "#48E848", "timestamp": 1748606591313, "isMod": false, "isSubscriber": false}, {"id": "IdQiLDNV6_oE4dqxnu8o7", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON> - \"10 Second Timer\" to the queue at #29 (playing ~in  1 hour 27 mins) https://youtu.be/tCDvOQI3pco", "color": "#5B99FF", "timestamp": 1748606591903, "isMod": true, "isSubscriber": false}, {"id": "yDesqaG1-nFSGokuFjd6q", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: video too short (30 sec min)", "color": "#7C7CE1", "timestamp": 1748606591979, "isMod": true, "isSubscriber": false}, {"id": "ZtSJxyK5vIy4AfQZC3PyZ", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr 10 second timer", "color": "#48E848", "timestamp": 1748606594754, "isMod": false, "isSubscriber": false}, {"id": "LsmR1CaVtjq0OzS6LIPAC", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON> - \"10 Second Timer\" to the queue at #29 (playing ~in  1 hour 27 mins) https://youtu.be/tCDvOQI3pco", "color": "#5B99FF", "timestamp": 1748606595294, "isMod": true, "isSubscriber": false}, {"id": "eeZOBQZO3yni0s_kwCWbC", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-b-bass down low", "color": "#48E848", "timestamp": 1748606985778, "isMod": false, "isSubscriber": false}, {"id": "1Iqu9QhSnHZA7VKPD0RUE", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748606986233, "isMod": true, "isSubscriber": false}, {"id": "ewjq3o_CMuZu7NxaF-cIq", "username": "streamelements", "message": "@lordwolfyyy, added r u s s e l b u c k - \"b-b-BASS DOWN LOW (Official Audio)\" to the queue at #22 (playing ~in  1 hour 11 mins) https://youtu.be/CE2F3m_6HhE", "color": "#5B99FF", "timestamp": 1748606986694, "isMod": true, "isSubscriber": false}, {"id": "kBu9WOqueG5O46HTuIqeE", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr bass down low", "color": "#48E848", "timestamp": 1748606989083, "isMod": false, "isSubscriber": false}, {"id": "auTSuV-k-agpbhx6z_lwI", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON><PERSON> - \"DEV - Bass Down Low (Explicit) ft. The Cataracs (Official Music Video)\" to the queue at #33 (playing ~in  1 hour 30 mins) https://youtu.be/OOAMfUJ3tsc", "color": "#5B99FF", "timestamp": 1748606989816, "isMod": true, "isSubscriber": false}, {"id": "IuzCDqzzQKKaqnLqbtDeO", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr bass dowm low", "color": "#48E848", "timestamp": 1748607056841, "isMod": false, "isSubscriber": false}, {"id": "twJLohfHI2pFinHOyb342", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748607057402, "isMod": true, "isSubscriber": false}, {"id": "-ZbnWmNsBfMcONGydslBx", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON><PERSON> - \"DEV - Bass Down Low (Explicit) ft. The Cataracs (Official Music Video)\" to the queue at #33 (playing ~in  1 hour 30 mins) https://youtu.be/OOAMfUJ3tsc", "color": "#5B99FF", "timestamp": 1748607057463, "isMod": true, "isSubscriber": false}, {"id": "aZ8bN8YqoipFgxZgoV686", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr b-bass down low", "color": "#48E848", "timestamp": 1748607199189, "isMod": false, "isSubscriber": false}, {"id": "Qf6qpaz6xnKebEzQZG2LL", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748607199700, "isMod": true, "isSubscriber": false}, {"id": "fpURUFVnkOJhA2KnAVHl8", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON><PERSON> - \"DEV - Bass Down Low (Explicit) ft. The Cataracs (Official Music Video)\" to the queue at #33 (playing ~in  1 hour 30 mins) https://youtu.be/OOAMfUJ3tsc", "color": "#5B99FF", "timestamp": 1748607200169, "isMod": true, "isSubscriber": false}, {"id": "Q1rD2hBTuCWQgsRZAKftp", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr runaway kanye", "color": "#48E848", "timestamp": 1748607421140, "isMod": false, "isSubscriber": false}, {"id": "eu4zanrlsZO_PFvsGzG6Z", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748607421774, "isMod": true, "isSubscriber": false}, {"id": "52_ZpN__aFG39DDrggCmn", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON> - \"<PERSON><PERSON><PERSON> (Lyrics) ft. <PERSON><PERSON><PERSON> T\" to the queue at #13 (playing ~in  36 mins 11 secs) https://youtu.be/4TVT7IOqH1Y", "color": "#5B99FF", "timestamp": 1748607421903, "isMod": true, "isSubscriber": false}, {"id": "Whos8Qd6QR4ho4TJimh7F", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr shoulder bolders", "color": "#48E848", "timestamp": 1748607528843, "isMod": false, "isSubscriber": false}, {"id": "ZpN7xifMsyVXltbb5sQda", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748607529392, "isMod": true, "isSubscriber": false}, {"id": "13dLb81v-ryuDj6_3-WaN", "username": "streamelements", "message": "@lordwolfyyy, added S3RL - \"Shoulder Boulders - S3RL\" to the queue at #37 (playing ~in  1 hour 46 mins) https://youtu.be/-VqDHb6B8xo", "color": "#5B99FF", "timestamp": 1748607529478, "isMod": true, "isSubscriber": false}, {"id": "9B47BqtUdFryYnkYiXOie", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr shoulder boulders", "color": "#48E848", "timestamp": 1748607862247, "isMod": false, "isSubscriber": false}, {"id": "d-Ove6fJhWoxzLm3YKya1", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748607862656, "isMod": true, "isSubscriber": false}, {"id": "Ej4wkP36CrAKu2XID-Beo", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON>-<PERSON> - \"S3RL - Shoulder Boulders HQ\" to the queue at #38 (playing ~in  1 hour 51 mins) https://youtu.be/jofBpB0OkcY", "color": "#5B99FF", "timestamp": 1748607863122, "isMod": true, "isSubscriber": false}, {"id": "uOph6SirSput0HEPu77tz", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr 10s timer", "color": "#48E848", "timestamp": 1748607878474, "isMod": false, "isSubscriber": false}, {"id": "qsD3alvy4SqD1mWWEvzjL", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: video too long (10 min max)", "color": "#7C7CE1", "timestamp": 1748607878972, "isMod": true, "isSubscriber": false}, {"id": "IJ-A9jdmDSyfiwmsKQ5H7", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON> - \"10 Second Timer\" to the queue at #29 (playing ~in  1 hour 27 mins) https://youtu.be/tCDvOQI3pco", "color": "#5B99FF", "timestamp": 1748607879079, "isMod": true, "isSubscriber": false}, {"id": "--6TvqJXKXOPK7n_UPa3V", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr shoulder bolders", "color": "#48E848", "timestamp": 1748607881870, "isMod": false, "isSubscriber": false}, {"id": "Vs1DzDyRJ_Ze1UX6S1Dkm", "username": "streamelements", "message": "@lordwolfyyy, added S3RL - \"Shoulder Boulders - S3RL\" to the queue at #37 (playing ~in  1 hour 46 mins) https://youtu.be/-VqDHb6B8xo", "color": "#5B99FF", "timestamp": 1748607882460, "isMod": true, "isSubscriber": false}, {"id": "yGuo9ozExLWORj_gwn8QP", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!sr bound 2", "color": "#48E848", "timestamp": 1748608183008, "isMod": false, "isSubscriber": false}, {"id": "UOhrlfL7xgzvcCC339Pm_", "username": "nightbot", "message": "@lordwolfyyy -> There was an error requesting the song. Error: The song request queue is full", "color": "#7C7CE1", "timestamp": 1748608183610, "isMod": true, "isSubscriber": false}, {"id": "jEGrUdoA5IMYfx-TLZ6aE", "username": "streamelements", "message": "@lordwolfyyy, added <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - \"Kanye West - Bound 2\" to the queue at #41 (playing ~in  2 hours) https://youtu.be/BBAtAM7vtgc", "color": "#5B99FF", "timestamp": 1748608183684, "isMod": true, "isSubscriber": false}, {"id": "pxEjS0iF9JSs1YXt7ZsOt", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748608206127, "isMod": false, "isSubscriber": false}, {"id": "SqO-gFIqjc44ldvXcffqL", "username": "streamelements", "message": "@lordwolfyy<PERSON> failed to get the current song.", "color": "#5B99FF", "timestamp": 1748608206339, "isMod": true, "isSubscriber": false}, {"id": "okv9ls4erG3JST1oFuU7r", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!nowplaying", "color": "#48E848", "timestamp": 1748608229274, "isMod": false, "isSubscriber": false}, {"id": "Oqdx9e4tELfzYrkTkeGUz", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!nowplaying", "color": "#48E848", "timestamp": 1748608487495, "isMod": false, "isSubscriber": false}, {"id": "0BTkJR2d1fkz9gA1Vwqd-", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748608492750, "isMod": false, "isSubscriber": false}, {"id": "mhAvn8mBAb0ItJccMxwFs", "username": "streamelements", "message": "@lordwolfyy<PERSON> failed to get the current song.", "color": "#5B99FF", "timestamp": 1748608492981, "isMod": true, "isSubscriber": false}, {"id": "_q2bRPBVKrQzlIvLTMont", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!song", "color": "#48E848", "timestamp": 1748608705056, "isMod": false, "isSubscriber": false}, {"id": "IBprLHIzuwM_XdHVfulpc", "username": "streamelements", "message": "@lordwolfyy<PERSON> failed to get the current song.", "color": "#5B99FF", "timestamp": 1748608705284, "isMod": true, "isSubscriber": false}, {"id": "1LxuyJv9CYe0E45M62rVV", "username": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "!!song", "color": "#48E848", "timestamp": 1748608894108, "isMod": false, "isSubscriber": false}]}, "metrics": {"totalChatMessages": 138, "totalCommands": 2, "totalSongRequests": 57, "startTime": 1748544496959}, "settings": {}}