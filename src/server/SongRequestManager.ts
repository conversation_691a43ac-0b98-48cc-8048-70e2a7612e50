import { nanoid } from 'nanoid';
import { Database } from './Database';
import { YouTubeSearchService } from './YouTubeSearchService';

type Song = {
  id: string;
  title: string;
  thumbnail: string;
  duration: number;
  requestedBy: string;
  source: string;
  sourceId: string;
  url?: string;
};

type SongRequestRestrictions = {
  enabled: boolean;
  subscribersOnly: boolean;
  modsOnly: boolean;
  vipsOnly: boolean;
  followersOnly: boolean;
  allowedUsers: string[]; // Specific usernames allowed
  blockedUsers: string[]; // Specific usernames blocked
  minFollowAge: number; // Days
  minAccountAge: number; // Days
};

// Initial songs for the queue (demo data)
const initialQueue: Song[] = [
  {
    id: nanoid(),
    title: 'Never Gonna Give You Up',
    thumbnail: 'https://i.ytimg.com/vi/dQw4w9WgXcQ/hqdefault.jpg',
    duration: 213,
    requestedBy: 'ViewerName123',
    source: 'youtube',
    sourceId: 'dQw4w9WgXcQ'
  },
  {
    id: nanoid(),
    title: 'Take On Me',
    thumbnail: 'https://i.ytimg.com/vi/djV11Xbc914/hqdefault.jpg',
    duration: 225,
    requestedBy: 'ModeratorUser',
    source: 'youtube',
    sourceId: 'djV11Xbc914'
  },
  {
    id: nanoid(),
    title: 'Billie Jean',
    thumbnail: 'https://i.ytimg.com/vi/Zi_XLOBDo_Y/hqdefault.jpg',
    duration: 294,
    requestedBy: 'RandomViewer42',
    source: 'youtube',
    sourceId: 'Zi_XLOBDo_Y'
  }
];

export class SongRequestManager {
  private db: Database;
  private currentSong: (Song & { currentTime: number }) | null = null;
  private playing: boolean = false;
  private songInterval: NodeJS.Timeout | null = null;
  private maxSongsPerUser = 3; // Maximum songs per user in queue
  private userRequestCounts = new Map<string, number>(); // Track requests per user
  private youtubeSearch: YouTubeSearchService;
  private songChangeCallback: ((song: any, action: 'started' | 'skipped') => void) | null = null;
  private restrictions: SongRequestRestrictions = {
    enabled: false,
    subscribersOnly: false,
    modsOnly: false,
    vipsOnly: false,
    followersOnly: false,
    allowedUsers: [],
    blockedUsers: [],
    minFollowAge: 0,
    minAccountAge: 0
  };

  constructor() {
    this.db = Database.getInstance();
    this.youtubeSearch = new YouTubeSearchService();
    this.updateUserRequestCounts();

    // Load current song from database
    const currentSong = this.db.getCurrentSong();
    if (currentSong) {
      this.currentSong = { ...currentSong, currentTime: 0 };
      this.playing = false; // Start paused
    }
  }

  private updateUserRequestCounts() {
    // Count current requests per user
    this.userRequestCounts.clear();
    const queue = this.db.getSongQueue();
    queue.forEach(song => {
      const count = this.userRequestCounts.get(song.requestedBy) || 0;
      this.userRequestCounts.set(song.requestedBy, count + 1);
    });
  }

  private startSongProgress(): void {
    if (this.songInterval) {
      clearInterval(this.songInterval);
    }

    this.songInterval = setInterval(() => {
      if (!this.currentSong || !this.playing) return;

      this.currentSong.currentTime += 1;

      // Auto-skip when song ends
      if (this.currentSong.currentTime >= this.currentSong.duration) {
        this.skip();
      }
    }, 1000);
  }

  public getQueue(): Song[] {
    return this.db.getSongQueue();
  }

  public getCurrentSong(): (Song & { currentTime: number }) | null {
    return this.currentSong;
  }

  public isPlaying(): boolean {
    return this.playing;
  }

  public addSong(song: Song, userstate?: any): { success: boolean; error?: string } {
    // Check if user has reached max songs limit
    const userCount = this.userRequestCounts.get(song.requestedBy) || 0;
    if (userCount >= this.maxSongsPerUser) {
      const error = `User ${song.requestedBy} has reached max songs limit (${this.maxSongsPerUser})`;
      console.log(`❌ ${error}`);
      return { success: false, error };
    }

    // Check user restrictions
    const restrictionCheck = this.canUserRequestSongs(song.requestedBy, userstate);
    if (!restrictionCheck.allowed) {
      console.log(`❌ User ${song.requestedBy} cannot request songs: ${restrictionCheck.reason}`);
      return { success: false, error: restrictionCheck.reason };
    }

    // Create unique song instance ID with prefix
    const uniqueInstanceId = `song_${nanoid()}`;

    const newSong = {
      ...song,
      id: uniqueInstanceId // Each song instance gets a unique ID
    };

    this.db.addSongToQueue(newSong);
    this.updateUserRequestCounts();

    // If no song is playing, start this one
    if (!this.currentSong) {
      this.skip();
    }

    console.log(`✅ Added song: ${newSong.title} by ${newSong.requestedBy} with ID: ${uniqueInstanceId}`);
    return { success: true };
  }

  public removeSong(id: string): void {
    this.db.removeSongFromQueue(id);
    this.updateUserRequestCounts();
  }

  public play(): void {
    const queue = this.db.getSongQueue();
    if (!this.currentSong && queue.length > 0) {
      const song = queue[0];
      this.db.removeSongFromQueue(song.id);

      // Always start with currentTime: 0 for new song instances
      this.currentSong = { ...song, currentTime: 0 };
      this.db.setCurrentSong(this.currentSong);
      this.updateUserRequestCounts();

      // Announce the new song
      if (this.songChangeCallback) {
        this.songChangeCallback(this.currentSong, 'started');
      }

      console.log(`✅ Started playback with currentTime: 0 (ID: ${song.id})`);
    }

    this.playing = true;
    this.startSongProgress();
  }

  public pause(): void {
    this.playing = false;

    if (this.songInterval) {
      clearInterval(this.songInterval);
      this.songInterval = null;
    }
  }

  public skip(): void {
    const previousSong = this.currentSong;
    const queue = this.db.getSongQueue();

    if (queue.length > 0) {
      const song = queue[0];
      console.log(`⏭️ Skipping to next song: ${song.title} (ID: ${song.id})`);
      this.db.removeSongFromQueue(song.id);

      // Announce the skip if there was a previous song
      if (previousSong && this.songChangeCallback) {
        this.songChangeCallback(previousSong, 'skipped');
      }

      // Always start with currentTime: 0 for new song instances
      this.currentSong = { ...song, currentTime: 0 };
      this.db.setCurrentSong(this.currentSong);
      this.playing = true;
      this.startSongProgress();
      this.updateUserRequestCounts();

      // Announce the new song
      if (this.songChangeCallback) {
        this.songChangeCallback(this.currentSong, 'started');
      }

      console.log(`✅ Started new song instance with currentTime: 0`);
    } else {
      console.log(`⏹️ No more songs in queue, stopping playback`);

      // Announce the skip if there was a previous song
      if (previousSong && this.songChangeCallback) {
        this.songChangeCallback(previousSong, 'skipped');
      }

      this.currentSong = null;
      this.db.setCurrentSong(null);
      this.playing = false;

      if (this.songInterval) {
        clearInterval(this.songInterval);
        this.songInterval = null;
      }
    }
  }

  public skipSpecificSong(songId: string): void {
    console.log(`⏭️ Attempting to skip specific song with ID: ${songId}`);

    // Only skip if this is the currently playing song
    if (this.currentSong && this.currentSong.id === songId) {
      console.log(`✅ Skipping current song: ${this.currentSong.title} (ID: ${songId})`);
      this.skip();
    } else {
      console.log(`❌ Song ID ${songId} is not currently playing, ignoring skip request`);
    }
  }

  public playSongById(id: string): void {
    const queue = this.db.getSongQueue();
    const song = queue.find(s => s.id === id);

    if (song) {
      const previousSong = this.currentSong;
      this.db.removeSongFromQueue(id);

      if (this.currentSong) {
        // Announce the skip of current song
        if (this.songChangeCallback) {
          this.songChangeCallback(this.currentSong, 'skipped');
        }

        // Put current song back in queue at the front
        const currentSongCopy = {
          ...this.currentSong,
          id: nanoid() // Generate new ID to avoid conflicts
        };
        this.db.addSongToQueue(currentSongCopy, 0); // Add to front
      }

      // Always start with currentTime: 0 for new song instances
      this.currentSong = { ...song, currentTime: 0 };
      this.db.setCurrentSong(this.currentSong);
      this.playing = true;
      this.startSongProgress();
      this.updateUserRequestCounts();

      // Announce the new song
      if (this.songChangeCallback) {
        this.songChangeCallback(this.currentSong, 'started');
      }

      console.log(`✅ Started specific song with currentTime: 0 (ID: ${id})`);
    }
  }

  public reorderQueue(sourceIndex: number, destinationIndex: number): void {
    if (sourceIndex === destinationIndex) return;

    const queue = [...this.db.getSongQueue()];
    const [removed] = queue.splice(sourceIndex, 1);
    queue.splice(destinationIndex, 0, removed);

    this.db.setSongQueue(queue);
  }

  // Search songs using integrated YouTube search (no external API needed)
  public async searchSongs(query: string): Promise<Song[]> {
    try {
      console.log(`🔍 Searching YouTube for: "${query}"`);

      // Check if query is a YouTube or Piped URL
      const videoInfo = await this.youtubeSearch.getVideoInfo(query);
      if (videoInfo) {
        console.log(`✅ Found video from URL: ${videoInfo.title}`);
        return [{
          id: nanoid(),
          title: videoInfo.title,
          thumbnail: videoInfo.thumbnail,
          duration: videoInfo.duration,
          requestedBy: 'System',
          source: 'youtube',
          sourceId: this.youtubeSearch.extractVideoId(query) || '',
          url: videoInfo.url
        }];
      }

      // Search YouTube for the query as-is (no artist/title parsing)
      const searchResults = await this.youtubeSearch.search(query, 5);

      // Transform to our Song format
      const songs = searchResults.map((result) => ({
        id: nanoid(),
        title: result.title,
        thumbnail: result.thumbnail,
        duration: result.duration,
        requestedBy: 'System',
        source: 'youtube',
        sourceId: this.youtubeSearch.extractVideoId(result.url) || '',
        url: result.url
      }));

      console.log(`✅ Found ${songs.length} YouTube results for: "${query}"`);
      return songs;
    } catch (error) {
      console.error('❌ Error searching YouTube:', error);
      return [];
    }
  }

  // Get user's current request count
  public getUserRequestCount(username: string): number {
    return this.userRequestCounts.get(username) || 0;
  }

  // Get max songs per user limit
  public getMaxSongsPerUser(): number {
    return this.maxSongsPerUser;
  }

  // Set max songs per user limit
  public setMaxSongsPerUser(max: number): void {
    this.maxSongsPerUser = max;
  }

  // Set song change callback for chat announcements
  public setSongChangeCallback(callback: (song: any, action: 'started' | 'skipped') => void): void {
    this.songChangeCallback = callback;
  }

  // Check if user can request songs based on restrictions
  public canUserRequestSongs(username: string, userstate?: any): { allowed: boolean; reason?: string } {
    if (!this.restrictions.enabled) {
      return { allowed: true };
    }

    const lowerUsername = username.toLowerCase();

    // Check blocked users first
    if (this.restrictions.blockedUsers.some(user => user.toLowerCase() === lowerUsername)) {
      return { allowed: false, reason: 'You are blocked from requesting songs' };
    }

    // Check allowed users (bypasses all other restrictions)
    if (this.restrictions.allowedUsers.some(user => user.toLowerCase() === lowerUsername)) {
      return { allowed: true };
    }

    // Check if userstate is provided for role-based restrictions
    if (!userstate) {
      return { allowed: false, reason: 'Unable to verify user permissions' };
    }

    // Check broadcaster (always allowed)
    if (userstate.badges?.broadcaster) {
      return { allowed: true };
    }

    // Check mods only
    if (this.restrictions.modsOnly && !userstate.mod) {
      return { allowed: false, reason: 'Song requests are limited to moderators only' };
    }

    // Check VIPs only
    if (this.restrictions.vipsOnly && !userstate.badges?.vip) {
      return { allowed: false, reason: 'Song requests are limited to VIPs only' };
    }

    // Check subscribers only
    if (this.restrictions.subscribersOnly && !userstate.subscriber) {
      return { allowed: false, reason: 'Song requests are limited to subscribers only' };
    }

    // Check followers only (would need additional API integration)
    if (this.restrictions.followersOnly) {
      // For now, we'll assume all users are followers unless we have specific data
      // In a real implementation, you'd check against Twitch API
      console.log('⚠️ Follower check not implemented - allowing request');
    }

    return { allowed: true };
  }

  // Get current restrictions
  public getRestrictions(): SongRequestRestrictions {
    return { ...this.restrictions };
  }

  // Update restrictions
  public setRestrictions(restrictions: Partial<SongRequestRestrictions>): void {
    this.restrictions = { ...this.restrictions, ...restrictions };
    console.log('✅ Song request restrictions updated:', this.restrictions);
  }

  // Add user to allowed list
  public addAllowedUser(username: string): void {
    const lowerUsername = username.toLowerCase();
    if (!this.restrictions.allowedUsers.some(user => user.toLowerCase() === lowerUsername)) {
      this.restrictions.allowedUsers.push(username);
      console.log(`✅ Added ${username} to allowed users list`);
    }
  }

  // Remove user from allowed list
  public removeAllowedUser(username: string): void {
    const lowerUsername = username.toLowerCase();
    this.restrictions.allowedUsers = this.restrictions.allowedUsers.filter(
      user => user.toLowerCase() !== lowerUsername
    );
    console.log(`✅ Removed ${username} from allowed users list`);
  }

  // Add user to blocked list
  public addBlockedUser(username: string): void {
    const lowerUsername = username.toLowerCase();
    if (!this.restrictions.blockedUsers.some(user => user.toLowerCase() === lowerUsername)) {
      this.restrictions.blockedUsers.push(username);
      console.log(`✅ Added ${username} to blocked users list`);
    }
  }

  // Remove user from blocked list
  public removeBlockedUser(username: string): void {
    const lowerUsername = username.toLowerCase();
    this.restrictions.blockedUsers = this.restrictions.blockedUsers.filter(
      user => user.toLowerCase() !== lowerUsername
    );
    console.log(`✅ Removed ${username} from blocked users list`);
  }

  // Check if user can skip songs (mods and broadcaster only)
  public canUserSkipSongs(userstate?: any): { allowed: boolean; reason?: string } {
    if (!userstate) {
      return { allowed: false, reason: 'Unable to verify user permissions' };
    }

    // Check broadcaster (always allowed)
    if (userstate.badges?.broadcaster) {
      return { allowed: true };
    }

    // Check moderators
    if (userstate.mod) {
      return { allowed: true };
    }

    return { allowed: false, reason: 'Only moderators and the broadcaster can skip songs' };
  }
}