import { nanoid } from 'nanoid';
import { Database } from './Database';

type GiveawayEntry = {
  username: string;
  displayName?: string;
  timestamp: string;
  message: string;
  userType: 'viewer' | 'follower' | 'subscriber' | 'vip' | 'moderator';
  badges: string[];
  isEligible: boolean;
  userId?: string;
  followDate?: string;
  subTier?: number;
};

type GiveawayRestrictions = {
  subscribersOnly: boolean;
  followersOnly: boolean;
  excludeModerators: boolean;
  excludeVips: boolean;
  minFollowAge: number; // in days
  minAccountAge: number; // in days
  maxEntries: number;
  allowMultipleEntries: boolean;
};

type ActiveGiveaway = {
  id: string;
  title: string;
  prize: string;
  keyword: string;
  startTime: string;
  endTime: string;
  timeRemaining: string;
  isPaused: boolean;
  entries: GiveawayEntry[];
  eligibleEntries: GiveawayEntry[];
  winner?: GiveawayEntry;
  restrictions: GiveawayRestrictions;
  announceStart?: boolean;
  startMessage?: string;
  winnerMessage?: string;
};

type PastGiveaway = {
  id: string;
  title: string;
  prize: string;
  date: string;
  entriesCount: number;
  winner: string;
};



export class GiveawayManager {
  private db: Database;
  private timerInterval: NodeJS.Timeout | null = null;
  private chatCallback: ((message: string) => void) | null = null;

  constructor() {
    this.db = Database.getInstance();
  }

  public setChatCallback(callback: (message: string) => void) {
    this.chatCallback = callback;
  }

  public getActiveGiveaway(): ActiveGiveaway | null {
    return this.db.getActiveGiveaway();
  }

  public getPastGiveaways(): PastGiveaway[] {
    return this.db.getPastGiveaways();
  }

  public startGiveaway(giveaway: Partial<ActiveGiveaway> & { duration?: number }): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      this.endGiveaway();
    }

    const durationMinutes = giveaway.duration || 5; // Use provided duration or default to 5 minutes
    const endTime = new Date(Date.now() + durationMinutes * 60 * 1000).toISOString();

    const defaultRestrictions: GiveawayRestrictions = {
      subscribersOnly: false,
      followersOnly: false,
      excludeModerators: false,
      excludeVips: false,
      minFollowAge: 0,
      minAccountAge: 0,
      maxEntries: 1000,
      allowMultipleEntries: false
    };

    const newGiveaway: ActiveGiveaway = {
      id: nanoid(),
      title: giveaway.title || 'Giveaway',
      prize: giveaway.prize || 'Prize',
      keyword: giveaway.keyword || '!join',
      startTime: new Date().toISOString(),
      endTime,
      timeRemaining: `${durationMinutes}:00`,
      isPaused: false,
      entries: [],
      eligibleEntries: [],
      restrictions: giveaway.restrictions || defaultRestrictions,
      announceStart: giveaway.announceStart || false,
      startMessage: giveaway.startMessage || '',
      winnerMessage: giveaway.winnerMessage || 'Congrats {winner}, you just won {prize}!'
    };

    this.db.setActiveGiveaway(newGiveaway);

    // Send announcement message if enabled
    if (newGiveaway.announceStart && newGiveaway.startMessage && this.chatCallback) {
      const message = newGiveaway.startMessage
        .replace('{title}', newGiveaway.title)
        .replace('{prize}', newGiveaway.prize)
        .replace('{keyword}', newGiveaway.keyword);
      this.chatCallback(message);
    }

    // Start countdown timer
    this.startTimer(durationMinutes * 60);

    // Add some test entries for demo purposes
    setTimeout(() => {
      this.addEntry({ username: 'TestUser1', timestamp: new Date().toLocaleTimeString() });
      this.addEntry({ username: 'TestUser2', timestamp: new Date().toLocaleTimeString() });
      this.addEntry({ username: 'TestUser3', timestamp: new Date().toLocaleTimeString() });
      console.log('✅ Added test entries to giveaway');
    }, 2000);
  }

  private startTimer(seconds: number): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }

    let remainingSeconds = seconds;

    this.timerInterval = setInterval(() => {
      const activeGiveaway = this.db.getActiveGiveaway();
      if (!activeGiveaway || activeGiveaway.isPaused) return;

      remainingSeconds--;

      if (remainingSeconds <= 0) {
        clearInterval(this.timerInterval!);
        this.timerInterval = null;

        // Auto-end the giveaway when timer expires
        this.endGiveaway();
        return;
      }

      const minutes = Math.floor(remainingSeconds / 60);
      const secs = remainingSeconds % 60;

      // Update the giveaway in database
      const updatedGiveaway = {
        ...activeGiveaway,
        timeRemaining: `${minutes}:${secs.toString().padStart(2, '0')}`
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }, 1000);
  }

  public endGiveaway(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = null;
    }

    this.db.endActiveGiveaway();
  }

  public addEntry(entry: GiveawayEntry): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      const updatedGiveaway = {
        ...activeGiveaway,
        entries: [...activeGiveaway.entries, entry],
        eligibleEntries: entry.isEligible
          ? [...(activeGiveaway.eligibleEntries || []), entry]
          : (activeGiveaway.eligibleEntries || [])
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }
  }

  public addWinner(winner: GiveawayWinner): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      const updatedGiveaway = {
        ...activeGiveaway,
        winners: [...activeGiveaway.winners, winner]
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }
  }

  public resetGiveaway(): void {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (activeGiveaway) {
      const updatedGiveaway = {
        ...activeGiveaway,
        entries: [],
        winners: []
      };
      this.db.setActiveGiveaway(updatedGiveaway);
    }
  }

  // Method to handle giveaway entries from chat
  public handleGiveawayEntry(username: string, message: string, userstate?: any): boolean {
    const activeGiveaway = this.db.getActiveGiveaway();
    if (!activeGiveaway) return false;

    // Check if message contains the keyword
    if (message.toLowerCase().includes(activeGiveaway.keyword.toLowerCase())) {
      // Check if user already entered (unless multiple entries allowed)
      const alreadyEntered = activeGiveaway.entries.some(entry => entry.username === username);
      if (alreadyEntered && !activeGiveaway.restrictions.allowMultipleEntries) {
        console.log(`❌ ${username} already entered giveaway`);
        return false;
      }

      // Determine user type and badges from userstate
      const userType = this.getUserType(userstate);
      const badges = this.getUserBadges(userstate);

      // Check eligibility based on restrictions
      const isEligible = this.checkEligibility(userType, badges, activeGiveaway.restrictions);

      const entry: GiveawayEntry = {
        username,
        displayName: userstate?.['display-name'] || username,
        timestamp: new Date().toLocaleTimeString(),
        message,
        userType,
        badges,
        isEligible,
        userId: userstate?.['user-id'],
        subTier: userstate?.subscriber ? parseInt(userstate?.['msg-param-sub-plan'] || '1') : undefined
      };

      this.addEntry(entry);

      if (isEligible) {
        console.log(`✅ ${username} entered giveaway (eligible)`);
        return true;
      } else {
        console.log(`⚠️ ${username} entered giveaway (not eligible)`);
        return false;
      }
    }
    return false;
  }

  private getUserType(userstate: any): 'viewer' | 'follower' | 'subscriber' | 'vip' | 'moderator' {
    if (userstate?.mod) return 'moderator';
    if (userstate?.vip) return 'vip';
    if (userstate?.subscriber) return 'subscriber';
    if (userstate?.follower) return 'follower';
    return 'viewer';
  }

  private getUserBadges(userstate: any): string[] {
    const badges: string[] = [];
    if (userstate?.mod) badges.push('moderator');
    if (userstate?.vip) badges.push('vip');
    if (userstate?.subscriber) badges.push('subscriber');
    if (userstate?.follower) badges.push('follower');
    return badges;
  }

  private checkEligibility(userType: string, badges: string[], restrictions: GiveawayRestrictions): boolean {
    // Check subscriber requirement
    if (restrictions.subscribersOnly && userType !== 'subscriber') {
      return false;
    }

    // Check follower requirement
    if (restrictions.followersOnly && !['follower', 'subscriber', 'vip', 'moderator'].includes(userType)) {
      return false;
    }

    // Check moderator exclusion
    if (restrictions.excludeModerators && userType === 'moderator') {
      return false;
    }

    // Check VIP exclusion
    if (restrictions.excludeVips && userType === 'vip') {
      return false;
    }

    // TODO: Add account age and follow age checks when we have that data

    return true;
  }

  // Method to draw a winner
  public drawWinner(): GiveawayEntry | null {
    console.log('🎲 Drawing winner...');
    const activeGiveaway = this.db.getActiveGiveaway();

    if (!activeGiveaway) {
      console.log('❌ No active giveaway found');
      return null;
    }

    const eligibleEntries = activeGiveaway.eligibleEntries || [];
    if (eligibleEntries.length === 0) {
      console.log('❌ No eligible entries found in giveaway');
      return null;
    }

    console.log(`🎯 Found ${eligibleEntries.length} eligible entries out of ${activeGiveaway.entries.length} total`);

    // Randomly select a winner from eligible entries
    const randomIndex = Math.floor(Math.random() * eligibleEntries.length);
    const winnerEntry = eligibleEntries[randomIndex];

    console.log(`🏆 Selected winner: ${winnerEntry.username} (index ${randomIndex})`);

    // Update giveaway with winner
    const updatedGiveaway = {
      ...activeGiveaway,
      winner: winnerEntry
    };
    this.db.setActiveGiveaway(updatedGiveaway);

    console.log(`✅ Winner selected: ${winnerEntry.username}`);

    // Send congratulations message
    if (this.chatCallback) {
      const message = `🎉 Congratulations ${winnerEntry.username}! You just won ${activeGiveaway.prize}! 🎉`;
      console.log(`📢 Sending congratulations message: ${message}`);
      this.chatCallback(message);
    } else {
      console.log('❌ No chat callback available');
    }

    return winnerEntry;
  }

  // Method to get user profile data for modal
  public getUserProfile(username: string): { username: string; messages: any[]; stats: any } {
    const chatMessages = this.db.getChatMessages();
    const userMessages = chatMessages.filter(msg => msg.username.toLowerCase() === username.toLowerCase());

    const stats = {
      totalMessages: userMessages.length,
      firstSeen: userMessages.length > 0 ? new Date(userMessages[0].timestamp).toLocaleDateString() : 'Never',
      lastSeen: userMessages.length > 0 ? new Date(userMessages[userMessages.length - 1].timestamp).toLocaleDateString() : 'Never',
      isMod: userMessages.length > 0 ? userMessages[userMessages.length - 1].isMod : false,
      isSubscriber: userMessages.length > 0 ? userMessages[userMessages.length - 1].isSubscriber : false
    };

    return {
      username,
      messages: userMessages.slice(-50), // Last 50 messages
      stats
    };
  }
}