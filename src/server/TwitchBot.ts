import { nanoid } from 'nanoid';
import { getRandomColor } from '../lib/utils';
import tmi from 'tmi.js';
import dotenv from 'dotenv';
import { Database } from './Database';
import { CommandManager } from './CommandManager';

dotenv.config();

type ChatMessage = {
  id: string;
  username: string;
  message: string;
  color: string;
  timestamp: number;
  isMod: boolean;
  isSubscriber: boolean;
};

type CommandHandler = (channel: string, userstate: tmi.ChatUserstate, message: string, self: boolean) => void;

export class TwitchBot {
  private static instance: TwitchBot | null = null;
  private client: tmi.Client | null = null;
  private chatHistory: ChatMessage[] = [];
  private metrics = {
    chatMessages: 0,
    viewers: 0,
    commandsUsed: 0,
    uptime: '00:00:00'
  };
  private recentEvents: string[] = [];
  private connected = false;
  private startTime = Date.now();
  private commandHandlers = new Map<string, CommandHandler>();
  private songRequestCallback: ((username: string, song: string, userstate?: any) => Promise<{ success: boolean; song?: any; error?: string }>) | null = null;
  private songRequestManager: any = null;
  private metricsUpdateCallback: ((metrics: any) => void) | null = null;
  private eventsUpdateCallback: ((events: string[]) => void) | null = null;
  private chatUpdateCallback: ((messages: ChatMessage[]) => void) | null = null;
  private db: Database;
  private commandManager: CommandManager;
  private giveawayManager: any;

  public static getInstance(): TwitchBot {
    if (!TwitchBot.instance) {
      TwitchBot.instance = new TwitchBot();
    }
    return TwitchBot.instance;
  }

  constructor() {
    this.db = Database.getInstance();
    this.commandManager = new CommandManager();
    this.initializeTwitchClient();
    this.setupCommandHandlers();
    this.startUptimeTimer();
  }

  public setGiveawayManager(giveawayManager: any) {
    this.giveawayManager = giveawayManager;
  }

  private initializeTwitchClient() {
    const username = process.env.TWITCH_USERNAME;
    const token = process.env.TWITCH_OAUTH_TOKEN;
    const channel = process.env.TWITCH_CHANNEL;

    console.log('🔧 Initializing Twitch client...');
    console.log('📋 Username:', username ? '✅ Set' : '❌ Missing');
    console.log('📋 Token:', token ? '✅ Set' : '❌ Missing');
    console.log('📋 Channel:', channel ? `✅ Set (${channel})` : '❌ Missing');

    if (!username || !token || !channel) {
      console.warn('❌ Twitch credentials not found in environment variables. Running in demo mode.');
      return;
    }

    this.client = new tmi.Client({
      options: { debug: true },
      connection: {
        reconnect: true,
        secure: true
      },
      identity: {
        username: username,
        password: token
      },
      channels: [channel]
    });

    this.setupEventHandlers();
    this.connectToTwitch();
  }

  private setupEventHandlers() {
    if (!this.client) return;

    this.client.on('connected', (addr, port) => {
      console.log(`Connected to Twitch chat at ${addr}:${port}`);
      this.connected = true;
      this.addRecentEvent('Bot connected to Twitch chat');
    });

    this.client.on('disconnected', (reason) => {
      console.log(`Disconnected from Twitch: ${reason}`);
      this.connected = false;
      this.addRecentEvent('Bot disconnected from Twitch chat');
    });

    this.client.on('message', (channel, userstate, message, self) => {
      if (self) return;

      console.log(`📨 Received message from ${userstate.username}: ${message}`);

      this.handleChatMessage(channel, userstate, message);
      this.handleCommands(channel, userstate, message, self);
    });

    this.client.on('subscription', (_channel, username, method, _message, _userstate) => {
      this.addRecentEvent(`${username} subscribed (${method.plan}) - just now`);
    });

    this.client.on('cheer', (_channel, userstate, _message) => {
      const bits = userstate.bits;
      this.addRecentEvent(`${userstate.username} cheered ${bits} bits - just now`);
    });
  }

  private async connectToTwitch() {
    if (!this.client) return;

    try {
      await this.client.connect();
    } catch (error) {
      console.error('Failed to connect to Twitch:', error);
      this.addRecentEvent('Failed to connect to Twitch chat');
    }
  }

  private setupCommandHandlers() {
    console.log('🤖 Setting up command handlers...');

    // Song request command
    this.commandHandlers.set('sr', this.handleSongRequest.bind(this));
    this.commandHandlers.set('songrequest', this.handleSongRequest.bind(this));

    // Skip song command (mods and broadcaster only)
    this.commandHandlers.set('skip', this.handleSkipCommand.bind(this));
    this.commandHandlers.set('skipsong', this.handleSkipCommand.bind(this));

    // Note: !song is now handled by CommandManager as a default command
    // Keep alternative commands as built-in handlers for backup
    this.commandHandlers.set('currentsong', this.handleCurrentSongCommand.bind(this));
    this.commandHandlers.set('nowplaying', this.handleCurrentSongCommand.bind(this));
    this.commandHandlers.set('playing', this.handleCurrentSongCommand.bind(this));
    this.commandHandlers.set('music', this.handleCurrentSongCommand.bind(this));

    // Basic commands
    this.commandHandlers.set('uptime', this.handleUptimeCommand.bind(this));
    this.commandHandlers.set('queue', this.handleQueueCommand.bind(this));

    // Test command to verify our bot is working
    this.commandHandlers.set('bottest', this.handleBotTestCommand.bind(this));

    console.log('✅ Command handlers set up:', Array.from(this.commandHandlers.keys()));
  }

  private startUptimeTimer() {
    setInterval(() => {
      const elapsed = Date.now() - this.startTime;
      const hours = Math.floor(elapsed / (1000 * 60 * 60));
      const minutes = Math.floor((elapsed % (1000 * 60 * 60)) / (1000 * 60));
      const seconds = Math.floor((elapsed % (1000 * 60)) / 1000);

      this.metrics.uptime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }, 1000);
  }

  private handleChatMessage(_channel: string, userstate: tmi.ChatUserstate, message: string) {
    const chatMessage: ChatMessage = {
      id: nanoid(),
      username: userstate.username || 'Unknown',
      message,
      color: userstate.color || getRandomColor(),
      timestamp: Date.now(),
      isMod: userstate.mod || false,
      isSubscriber: userstate.subscriber || false
    };

    // Save to database
    this.db.addChatMessage(chatMessage);

    // Also keep in memory for quick access
    this.chatHistory.push(chatMessage);
    if (this.chatHistory.length > 100) {
      this.chatHistory = this.chatHistory.slice(-100);
    }

    // Update metrics from database
    const metrics = this.db.getMetrics();
    this.metrics = {
      chatMessages: metrics.chatMessages,
      viewers: metrics.viewers,
      commandsUsed: metrics.commandsUsed,
      uptime: metrics.uptime
    };

    // Check for giveaway entries
    if (this.giveawayManager) {
      const entered = this.giveawayManager.handleGiveawayEntry(userstate.username || 'Unknown', message, userstate);
      if (entered) {
        this.addRecentEvent(`${userstate.username} entered the giveaway`);
      }
    }

    // Notify about metrics update
    if (this.metricsUpdateCallback) {
      this.metricsUpdateCallback(this.metrics);
    }

    // Notify about chat update
    if (this.chatUpdateCallback) {
      this.chatUpdateCallback(this.db.getChatMessages());
    }
  }

  private handleCommands(channel: string, userstate: tmi.ChatUserstate, message: string, self: boolean) {
    if (self || !message.startsWith('!')) return;

    const args = message.slice(1).split(' ');
    const commandName = args[0].toLowerCase();
    const username = userstate.username || 'Unknown';

    console.log(`🤖 Processing command: !${commandName} from ${username}`);

    // Determine user level
    let userLevel = 'everyone';
    if (userstate.badges?.broadcaster) userLevel = 'broadcaster';
    else if (userstate.mod) userLevel = 'moderator';
    else if (userstate.subscriber) userLevel = 'subscriber';

    // Check built-in commands first
    const handler = this.commandHandlers.get(commandName);
    if (handler) {
      console.log(`✅ Found built-in handler for !${commandName}`);
      handler(channel, userstate, message, self);
      this.db.incrementCommandUsage(commandName);
    } else {
      console.log(`❌ No built-in handler for !${commandName}, trying custom commands`);
      // Try custom commands
      const result = this.commandManager.useCommand(commandName, username, userLevel);
      if (result.success && result.response) {
        this.client?.say(channel, result.response);
      }
    }

    // Update metrics
    const metrics = this.db.getMetrics();
    this.metrics = {
      chatMessages: metrics.chatMessages,
      viewers: metrics.viewers,
      commandsUsed: metrics.commandsUsed,
      uptime: metrics.uptime
    };

    // Notify about metrics update
    if (this.metricsUpdateCallback) {
      this.metricsUpdateCallback(this.metrics);
    }
  }

  private addRecentEvent(event: string) {
    this.recentEvents.unshift(event);
    if (this.recentEvents.length > 10) {
      this.recentEvents = this.recentEvents.slice(0, 10);
    }

    // Notify about events update
    if (this.eventsUpdateCallback) {
      this.eventsUpdateCallback(this.recentEvents);
    }
  }

  private async handleSongRequest(channel: string, userstate: tmi.ChatUserstate, message: string, _self: boolean) {
    const args = message.slice(1).split(' ');
    const songQuery = args.slice(1).join(' ');

    if (!songQuery) {
      this.client?.say(channel, `@${userstate.username} Please provide a song to request! Usage: !sr <song name or URL>`);
      return;
    }

    const username = userstate.username || 'Unknown';

    // Call the song request callback if it's set
    if (this.songRequestCallback) {
      try {
        const result = await this.songRequestCallback(username, songQuery, userstate);
        if (result.success) {
          this.client?.say(channel, `@${username} ✅ Added "${result.song.title}" to the queue!`);
        } else {
          this.client?.say(channel, `@${username} ❌ ${result.error}`);
        }
      } catch (error) {
        console.error('Song request error:', error);
        this.client?.say(channel, `@${username} ❌ Failed to process song request. Please try again.`);
      }
    } else {
      this.client?.say(channel, `@${username} Song requests are currently disabled.`);
    }
  }

  private handleSkipCommand(channel: string, userstate: tmi.ChatUserstate, _message: string, _self: boolean) {
    console.log(`⏭️ !skip command called by ${userstate.username}`);

    if (!this.songRequestManager) {
      console.log('❌ No song request manager available');
      this.client?.say(channel, `@${userstate.username} Song requests are currently disabled.`);
      return;
    }

    // Check if user can skip songs
    const canSkip = this.songRequestManager.canUserSkipSongs(userstate);
    if (!canSkip.allowed) {
      console.log(`❌ User ${userstate.username} cannot skip songs: ${canSkip.reason}`);
      this.client?.say(channel, `@${userstate.username} ${canSkip.reason}`);
      return;
    }

    const currentSong = this.songRequestManager.getCurrentSong();
    if (!currentSong) {
      console.log('❌ No current song playing');
      this.client?.say(channel, `@${userstate.username} No song is currently playing.`);
      return;
    }

    console.log(`✅ ${userstate.username} is skipping: ${currentSong.title}`);
    this.songRequestManager.skip();
    this.client?.say(channel, `⏭️ ${userstate.username} skipped "${currentSong.title}"`);
  }

  private handleUptimeCommand(channel: string, userstate: tmi.ChatUserstate, _message: string, _self: boolean) {
    this.client?.say(channel, `@${userstate.username} Stream has been live for ${this.metrics.uptime}`);
  }

  private handleQueueCommand(channel: string, userstate: tmi.ChatUserstate, _message: string, _self: boolean) {
    // This would show current queue info - for now just a placeholder
    this.client?.say(channel, `@${userstate.username} Check the song queue on the stream overlay!`);
  }

  private handleBotTestCommand(channel: string, userstate: tmi.ChatUserstate, _message: string, _self: boolean) {
    console.log(`🧪 Bot test command called by ${userstate.username}`);
    const message = `@${userstate.username} ✅ Bot is working! Commands are being processed correctly.`;
    console.log(`📢 Sending test response: ${message}`);
    this.client?.say(channel, message);
  }

  private handleCurrentSongCommand(channel: string, userstate: tmi.ChatUserstate, _message: string, _self: boolean) {
    console.log(`🎵 !song command called by ${userstate.username} in channel: ${channel}`);

    if (!this.songRequestManager) {
      console.log('❌ No song request manager available');
      this.client?.say(channel, `@${userstate.username} Song requests are currently disabled.`);
      return;
    }

    const currentSong = this.songRequestManager.getCurrentSong();
    console.log('🎵 Current song:', currentSong);

    if (!currentSong) {
      console.log('❌ No current song playing');
      this.client?.say(channel, `@${userstate.username} No song is currently playing.`);
      return;
    }

    // Format duration
    const formatDuration = (seconds: number) => {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    const currentTime = formatDuration(currentSong.currentTime || 0);
    const totalTime = formatDuration(currentSong.duration);
    const message = `@${userstate.username} 🎵 Now Playing: "${currentSong.title}" by ${currentSong.requestedBy} [${currentTime}/${totalTime}]`;

    console.log(`📢 Sending !song response to ${channel}: ${message}`);

    try {
      this.client?.say(channel, message);
      console.log('✅ Message sent successfully');
    } catch (error) {
      console.error('❌ Error sending !song response:', error);
    }
  }

  // Public method to set song request callback
  public setSongRequestCallback(callback: (username: string, song: string, userstate?: any) => Promise<{ success: boolean; song?: any; error?: string }>) {
    this.songRequestCallback = callback;
  }

  // Public method to set song request manager reference
  public setSongRequestManager(songRequestManager: any) {
    this.songRequestManager = songRequestManager;
    // Also connect it to the command manager for song variables
    this.commandManager.setSongRequestManager(songRequestManager);
  }

  // Public method to announce song changes
  public announceSongChange(song: any, action: 'started' | 'skipped') {
    console.log(`🎵 Attempting to announce song change: ${action} - ${song.title}`);

    if (!this.client || !this.connected) {
      console.log('❌ Cannot announce: client not connected');
      return;
    }

    const channel = process.env.TWITCH_CHANNEL;
    if (!channel) {
      console.log('❌ Cannot announce: no channel configured');
      return;
    }

    // Ensure channel has # prefix
    const channelName = channel.startsWith('#') ? channel : `#${channel}`;

    // Format duration
    const formatDuration = (seconds: number) => {
      const mins = Math.floor(seconds / 60);
      const secs = Math.floor(seconds % 60);
      return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    try {
      if (action === 'started') {
        const duration = formatDuration(song.duration);
        const message = `🎵 Now Playing: "${song.title}" by ${song.requestedBy} [${duration}]`;
        console.log(`📢 Sending started message: ${message}`);
        this.client.say(channelName, message);
      } else if (action === 'skipped') {
        const message = `⏭️ Skipped: "${song.title}"`;
        console.log(`📢 Sending skipped message: ${message}`);
        this.client.say(channelName, message);
      }
    } catch (error) {
      console.error('❌ Error sending song announcement:', error);
    }
  }

  // Public method to set metrics update callback
  public setMetricsUpdateCallback(callback: (metrics: any) => void) {
    this.metricsUpdateCallback = callback;
  }

  // Public method to set events update callback
  public setEventsUpdateCallback(callback: (events: string[]) => void) {
    this.eventsUpdateCallback = callback;
  }

  // Public method to set chat update callback
  public setChatUpdateCallback(callback: (messages: ChatMessage[]) => void) {
    this.chatUpdateCallback = callback;
  }

  // Public method to check connection status
  public isConnected(): boolean {
    return this.connected;
  }



  public sendMessage(message: string) {
    const channel = process.env.TWITCH_CHANNEL;

    // Send to Twitch if connected
    if (this.client && this.connected && channel) {
      // Ensure channel has # prefix
      const channelName = channel.startsWith('#') ? channel : `#${channel}`;
      console.log(`📢 Sending message to ${channelName}: ${message}`);
      this.client.say(channelName, message);
    } else {
      console.log('❌ Cannot send message: not connected or no channel');
    }

    // Also add to local chat history for UI
    const chatMessage: ChatMessage = {
      id: nanoid(),
      username: process.env.TWITCH_USERNAME || 'StreamerName',
      message,
      color: '#9146FF',
      timestamp: Date.now(),
      isMod: true,
      isSubscriber: true
    };

    this.chatHistory.push(chatMessage);
    this.metrics.chatMessages++;

    return chatMessage;
  }

  public getChatHistory() {
    return this.db.getChatMessages();
  }

  public getMetrics() {
    return this.db.getMetrics();
  }

  public getRecentEvents() {
    return this.recentEvents;
  }
}