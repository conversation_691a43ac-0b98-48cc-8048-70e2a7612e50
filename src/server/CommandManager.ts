import { nanoid } from 'nanoid';
import { Database } from './Database';

export type Command = {
  id: string;
  name: string;
  response: string;
  userLevel: string;
  cooldown: number;
  enabled: boolean;
  usageCount: number;
};

export class CommandManager {
  private db: Database;
  private cooldowns = new Map<string, number>();
  private songRequestManager: any = null;

  constructor() {
    this.db = Database.getInstance();
    this.initializeDefaultCommands();
  }

  // Set song request manager reference for song variables
  public setSongRequestManager(songRequestManager: any) {
    this.songRequestManager = songRequestManager;
  }

  public getCommands(): Command[] {
    return this.db.getCommands();
  }

  public getCommand(name: string): Command | undefined {
    return this.db.getCommands().find(cmd => cmd.name === name);
  }

  public addCommand(command: Command): void {
    const newCommand = {
      ...command,
      id: command.id || nanoid(),
      usageCount: command.usageCount || 0
    };
    this.db.addCommand(newCommand);
  }

  public updateCommand(command: Command): void {
    this.db.updateCommand(command);
  }

  public deleteCommand(id: string): void {
    this.db.deleteCommand(id);
  }

  public incrementUsage(name: string): void {
    this.db.incrementCommandUsage(name);
  }

  // Check if user can use command and handle cooldowns
  public canUseCommand(commandName: string, username: string, userLevel: string): { canUse: boolean; reason?: string } {
    const command = this.getCommand(commandName);

    if (!command) {
      return { canUse: false, reason: 'Command not found' };
    }

    if (!command.enabled) {
      return { canUse: false, reason: 'Command is disabled' };
    }

    // Check user level permissions
    if (!this.hasPermission(userLevel, command.userLevel)) {
      return { canUse: false, reason: 'Insufficient permissions' };
    }

    // Check cooldown
    const cooldownKey = `${commandName}:${username}`;
    const lastUsed = this.cooldowns.get(cooldownKey) || 0;
    const now = Date.now();
    const cooldownMs = command.cooldown * 1000;

    if (now - lastUsed < cooldownMs) {
      const remainingSeconds = Math.ceil((cooldownMs - (now - lastUsed)) / 1000);
      return { canUse: false, reason: `Command on cooldown for ${remainingSeconds}s` };
    }

    return { canUse: true };
  }

  public useCommand(commandName: string, username: string, userLevel: string): { success: boolean; response?: string; reason?: string } {
    const canUse = this.canUseCommand(commandName, username, userLevel);

    if (!canUse.canUse) {
      return { success: false, reason: canUse.reason };
    }

    const command = this.getCommand(commandName);
    if (!command) {
      return { success: false, reason: 'Command not found' };
    }

    // Set cooldown
    const cooldownKey = `${commandName}:${username}`;
    this.cooldowns.set(cooldownKey, Date.now());

    // Increment usage
    this.incrementUsage(commandName);

    // Process response with variables
    const response = this.processCommandResponse(command.response, username);

    return { success: true, response };
  }

  private hasPermission(userLevel: string, requiredLevel: string): boolean {
    const levels = ['everyone', 'subscriber', 'moderator', 'broadcaster'];
    const userLevelIndex = levels.indexOf(userLevel);
    const requiredLevelIndex = levels.indexOf(requiredLevel);

    return userLevelIndex >= requiredLevelIndex;
  }

  private initializeDefaultCommands() {
    const existingCommands = this.db.getCommands();

    // Check if !song command already exists
    const songCommandExists = existingCommands.some(cmd => cmd.name === 'song');

    if (!songCommandExists) {
      const defaultSongCommand: Command = {
        id: nanoid(),
        name: 'song',
        response: '🎵 Now Playing: "{track}" by {artist} - requested by {requester} [{currentTime}/{duration}]',
        userLevel: 'everyone',
        cooldown: 5,
        enabled: true,
        usageCount: 0
      };

      this.db.addCommand(defaultSongCommand);
      console.log('✅ Added default !song command');
    }
  }

  private processCommandResponse(response: string, username: string): string {
    const db = Database.getInstance();
    const metrics = db.getMetrics();

    // Get current song info if available
    let songInfo = {
      track: 'No song playing',
      artist: 'Unknown',
      requester: 'Unknown',
      currentTime: '0:00',
      duration: '0:00'
    };

    if (this.songRequestManager) {
      const currentSong = this.songRequestManager.getCurrentSong();
      if (currentSong) {
        // Parse title to extract artist and track (common formats: "Artist - Track" or just "Track")
        const titleParts = currentSong.title.split(' - ');
        if (titleParts.length >= 2) {
          songInfo.artist = titleParts[0].trim();
          songInfo.track = titleParts.slice(1).join(' - ').trim();
        } else {
          songInfo.track = currentSong.title;
          songInfo.artist = 'Unknown Artist';
        }

        songInfo.requester = currentSong.requestedBy;

        // Format duration
        const formatDuration = (seconds: number) => {
          const mins = Math.floor(seconds / 60);
          const secs = Math.floor(seconds % 60);
          return `${mins}:${secs.toString().padStart(2, '0')}`;
        };

        songInfo.currentTime = formatDuration(currentSong.currentTime || 0);
        songInfo.duration = formatDuration(currentSong.duration);
      }
    }

    return response
      .replace(/{user}/g, username)
      .replace(/{uptime}/g, metrics.uptime)
      .replace(/{chatMessages}/g, metrics.chatMessages.toString())
      .replace(/{commands}/g, metrics.commandsUsed.toString())
      .replace(/{track}/g, songInfo.track)
      .replace(/{artist}/g, songInfo.artist)
      .replace(/{requester}/g, songInfo.requester)
      .replace(/{currentTime}/g, songInfo.currentTime)
      .replace(/{duration}/g, songInfo.duration);
  }
}