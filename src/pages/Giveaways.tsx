import { useState } from 'react';
import {
  Gift,
  Users,
  Clock,
  PlayCircle,
  PauseCircle,
  RefreshCw,
  Eye,
  MessageSquare,
  Trophy,
  Shield,
  Star,
  Heart,
  Crown,
  Settings,
  UserCheck,
  UserX,
  Timer,
  Hash
} from 'lucide-react';
import { useGiveaways } from '../hooks/useGiveaways';
import { useSocket } from '../hooks/useSocket';
import UserProfileModal from '../components/UserProfileModal';

const Giveaways = () => {
  const socket = useSocket();
  const {
    activeGiveaway,
    userProfile,
    showUserModal,
    isWinnerModal,
    selectedWinner,
    startGiveaway,
    endGiveaway,
    drawWinner,
    confirmWinner,
    rerollWinner,
    resetGiveaway,
    getUserProfile,
    viewUserProfile,
    closeUserModal,
    closeWinnerModal
  } = useGiveaways();

  const [newGiveaway, setNewGiveaway] = useState({
    title: '',
    prize: '',
    duration: 5,
    keyword: '!join',
    restrictions: {
      subscribersOnly: false,
      followersOnly: false,
      excludeModerators: false,
      excludeVips: false,
      minFollowAge: 0,
      minAccountAge: 0,
      maxEntries: 1000,
      allowMultipleEntries: false
    },
    announceStart: true,
    startMessage: '🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉',
    winnerMessage: 'Congrats {winner}, you just won {prize}!'
  });

  const [showAdvanced, setShowAdvanced] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    if (name.startsWith('restrictions.')) {
      const restrictionKey = name.split('.')[1];
      setNewGiveaway(prev => ({
        ...prev,
        restrictions: {
          ...prev.restrictions,
          [restrictionKey]: type === 'checkbox' ? checked : type === 'number' ? Number(value) : value
        }
      }));
    } else {
      setNewGiveaway(prev => ({
        ...prev,
        [name]: type === 'number' ? Number(value) : type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleStartGiveaway = (e: React.FormEvent) => {
    e.preventDefault();
    startGiveaway(newGiveaway);
  };

  const getEligibilityIcon = (userType: string) => {
    switch (userType) {
      case 'moderator': return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'subscriber': return <Star className="w-4 h-4 text-purple-500" />;
      case 'vip': return <Shield className="w-4 h-4 text-green-500" />;
      case 'follower': return <Heart className="w-4 h-4 text-red-500" />;
      default: return <Users className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-2xl font-bold">Giveaways</h1>
        <p className="text-gray-400">Nightbot-style giveaway system with advanced entry restrictions</p>
      </div>

      {activeGiveaway ? (
        <div className="card border-purple-500 bg-gradient-to-r from-purple-900/20 to-blue-900/20">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <Trophy size={24} className="text-yellow-500" />
                <h2 className="text-xl font-bold">Active Giveaway</h2>
                <span className="px-2 py-1 bg-green-600 text-white text-xs rounded-full animate-pulse">
                  LIVE
                </span>
              </div>
              <h3 className="text-3xl mb-2 font-bold bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent">
                {activeGiveaway.title}
              </h3>
              <p className="text-gray-300 text-lg">🎁 Prize: <span className="font-semibold">{activeGiveaway.prize}</span></p>
              <p className="text-gray-400 text-sm mt-1">
                Entry keyword: <span className="font-mono bg-gray-800 px-2 py-1 rounded">{activeGiveaway.keyword}</span>
              </p>
            </div>

            <div className="flex items-center space-x-6">
              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Timer className="w-5 h-5 text-orange-400" />
                  <p className="text-sm text-gray-400">Time Remaining</p>
                </div>
                <p className="text-3xl font-bold text-orange-400">
                  {activeGiveaway.timeRemaining || '--:--'}
                </p>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <Users className="w-5 h-5 text-blue-400" />
                  <p className="text-sm text-gray-400">Total Entries</p>
                </div>
                <p className="text-3xl font-bold text-blue-400">{activeGiveaway.entries.length}</p>
              </div>

              <div className="text-center">
                <div className="flex items-center justify-center space-x-1 mb-1">
                  <UserCheck className="w-5 h-5 text-green-400" />
                  <p className="text-sm text-gray-400">Eligible</p>
                </div>
                <p className="text-3xl font-bold text-green-400">{activeGiveaway.eligibleEntries?.length || 0}</p>
              </div>

              <div className="flex flex-col space-y-2">
                <button
                  className="p-2 rounded-full hover:bg-gray-700 text-gray-300 hover:text-white transition-colors"
                  title={activeGiveaway.isPaused ? "Resume Giveaway" : "Pause Giveaway"}
                >
                  {activeGiveaway.isPaused ?
                    <PlayCircle size={24} /> :
                    <PauseCircle size={24} />
                  }
                </button>
                <button
                  className="p-2 rounded-full hover:bg-gray-700 text-gray-300 hover:text-white transition-colors"
                  title="Reset Giveaway"
                  onClick={resetGiveaway}
                >
                  <RefreshCw size={24} />
                </button>
              </div>
            </div>
          </div>

          {/* Eligible Entries Only */}
          <div className="mt-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-bold text-lg flex items-center space-x-2">
                <UserCheck className="w-5 h-5 text-green-400" />
                <span>Eligible Entries ({activeGiveaway.eligibleEntries?.length || 0})</span>
              </h3>
              <div className="flex items-center space-x-3">
                <button
                  className="btn btn-primary flex items-center space-x-2"
                  onClick={drawWinner}
                  disabled={(activeGiveaway.eligibleEntries?.length || 0) === 0}
                >
                  <Trophy className="w-4 h-4" />
                  <span>Draw Winner</span>
                  {(activeGiveaway.eligibleEntries?.length || 0) === 0 && <span>(No Eligible Entries)</span>}
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={() => {
                    const testUser = `TestUser${Math.floor(Math.random() * 1000)}`;
                    if (socket) {
                      socket.emit('addTestEntry', testUser);
                    }
                  }}
                >
                  Add Test Entry
                </button>
                <button
                  className="btn btn-danger"
                  onClick={endGiveaway}
                >
                  End Giveaway
                </button>
              </div>
            </div>

            <div className="bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto">
              {(activeGiveaway.eligibleEntries?.length || 0) > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
                  {activeGiveaway.eligibleEntries?.map((entry, i) => (
                    <div key={i} className={`p-3 rounded-lg border transition-all hover:scale-105 cursor-pointer ${
                      entry.isEligible
                        ? 'bg-green-900/20 border-green-700 hover:bg-green-900/30'
                        : 'bg-red-900/20 border-red-700 hover:bg-red-900/30'
                    }`}>
                      <button
                        onClick={() => viewUserProfile(entry.username)}
                        className="w-full text-left"
                      >
                        <div className="flex items-center justify-between mb-2">
                          <div className="flex items-center space-x-2">
                            {getEligibilityIcon(entry.userType)}
                            <span className="font-medium">{entry.displayName || entry.username}</span>
                          </div>
                          <Eye className="w-4 h-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                        </div>
                        <div className="flex items-center space-x-1 mb-1">
                          {entry.badges?.map((badge, idx) => (
                            <span key={idx} className="text-xs px-1 py-0.5 bg-gray-700 rounded text-gray-300">
                              {badge}
                            </span>
                          ))}
                        </div>
                        <p className="text-xs text-gray-400 truncate">{entry.message}</p>
                        <p className="text-xs text-gray-500 mt-1">{entry.timestamp}</p>
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-12 text-gray-500">
                  <UserX className="w-16 h-16 mb-4 opacity-50" />
                  <p className="text-lg font-medium">No eligible entries yet</p>
                  <p className="text-sm">Waiting for viewers to enter with {activeGiveaway.keyword}</p>
                </div>
              )}
            </div>

            {/* Entry Restrictions Display */}
            {activeGiveaway.restrictions && (
              <div className="mt-4 p-4 bg-gray-800 rounded-lg">
                <h4 className="font-medium mb-2 flex items-center space-x-2">
                  <Settings className="w-4 h-4" />
                  <span>Entry Restrictions</span>
                </h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                  {activeGiveaway.restrictions.subscribersOnly && (
                    <span className="flex items-center space-x-1 text-purple-400">
                      <Star className="w-3 h-3" />
                      <span>Subscribers Only</span>
                    </span>
                  )}
                  {activeGiveaway.restrictions.followersOnly && (
                    <span className="flex items-center space-x-1 text-red-400">
                      <Heart className="w-3 h-3" />
                      <span>Followers Only</span>
                    </span>
                  )}
                  {activeGiveaway.restrictions.excludeModerators && (
                    <span className="flex items-center space-x-1 text-yellow-400">
                      <Crown className="w-3 h-3" />
                      <span>No Moderators</span>
                    </span>
                  )}
                  {activeGiveaway.restrictions.excludeVips && (
                    <span className="flex items-center space-x-1 text-green-400">
                      <Shield className="w-3 h-3" />
                      <span>No VIPs</span>
                    </span>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      ) : (
        <div className="card">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-bold">Start a New Giveaway</h2>
              <p className="text-gray-400">Configure your giveaway with advanced entry restrictions</p>
            </div>
            <button
              type="button"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="flex items-center space-x-2 text-purple-400 hover:text-purple-300 transition-colors"
            >
              <Settings className="w-4 h-4" />
              <span>{showAdvanced ? 'Hide' : 'Show'} Advanced Settings</span>
            </button>
          </div>

          <form onSubmit={handleStartGiveaway} className="space-y-6">
            {/* Basic Settings */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-2 font-medium" htmlFor="title">
                  Giveaway Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  value={newGiveaway.title}
                  onChange={handleChange}
                  className="input w-full"
                  placeholder="1000 Subscribers Celebration!"
                  required
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2 font-medium" htmlFor="prize">
                  Prize
                </label>
                <input
                  type="text"
                  id="prize"
                  name="prize"
                  value={newGiveaway.prize}
                  onChange={handleChange}
                  className="input w-full"
                  placeholder="$50 Steam Gift Card"
                  required
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 mb-2 font-medium" htmlFor="duration">
                  Duration (minutes)
                </label>
                <input
                  type="number"
                  id="duration"
                  name="duration"
                  value={newGiveaway.duration}
                  onChange={handleChange}
                  className="input w-full"
                  min={1}
                  max={120}
                  required
                />
              </div>

              <div>
                <label className="block text-gray-300 mb-2 font-medium" htmlFor="keyword">
                  Entry Keyword
                </label>
                <input
                  type="text"
                  id="keyword"
                  name="keyword"
                  value={newGiveaway.keyword}
                  onChange={handleChange}
                  className="input w-full"
                  placeholder="!join"
                  required
                />
              </div>
            </div>

            {/* Advanced Restrictions */}
            {showAdvanced && (
              <div className="border-t border-gray-700 pt-6">
                <h3 className="text-lg font-bold mb-4 flex items-center space-x-2">
                  <Shield className="w-5 h-5 text-blue-400" />
                  <span>Entry Restrictions</span>
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* User Type Restrictions */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-300">User Type Requirements</h4>

                    <div className="space-y-3">
                      <label className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          name="restrictions.subscribersOnly"
                          checked={newGiveaway.restrictions.subscribersOnly}
                          onChange={handleChange}
                          className="w-4 h-4 text-purple-600 bg-gray-700 border-gray-600 rounded focus:ring-purple-500"
                        />
                        <div className="flex items-center space-x-2">
                          <Star className="w-4 h-4 text-purple-500" />
                          <span>Subscribers Only</span>
                        </div>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          name="restrictions.followersOnly"
                          checked={newGiveaway.restrictions.followersOnly}
                          onChange={handleChange}
                          className="w-4 h-4 text-red-600 bg-gray-700 border-gray-600 rounded focus:ring-red-500"
                        />
                        <div className="flex items-center space-x-2">
                          <Heart className="w-4 h-4 text-red-500" />
                          <span>Followers Only</span>
                        </div>
                      </label>
                    </div>
                  </div>

                  {/* User Type Exclusions */}
                  <div className="space-y-4">
                    <h4 className="font-medium text-gray-300">User Type Exclusions</h4>

                    <div className="space-y-3">
                      <label className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          name="restrictions.excludeModerators"
                          checked={newGiveaway.restrictions.excludeModerators}
                          onChange={handleChange}
                          className="w-4 h-4 text-yellow-600 bg-gray-700 border-gray-600 rounded focus:ring-yellow-500"
                        />
                        <div className="flex items-center space-x-2">
                          <Crown className="w-4 h-4 text-yellow-500" />
                          <span>Exclude Moderators</span>
                        </div>
                      </label>

                      <label className="flex items-center space-x-3">
                        <input
                          type="checkbox"
                          name="restrictions.excludeVips"
                          checked={newGiveaway.restrictions.excludeVips}
                          onChange={handleChange}
                          className="w-4 h-4 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500"
                        />
                        <div className="flex items-center space-x-2">
                          <Shield className="w-4 h-4 text-green-500" />
                          <span>Exclude VIPs</span>
                        </div>
                      </label>
                    </div>
                  </div>
                </div>

                {/* Age Requirements */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                  <div>
                    <label className="block text-gray-300 mb-2" htmlFor="restrictions.minFollowAge">
                      Min Follow Age (days)
                    </label>
                    <input
                      type="number"
                      name="restrictions.minFollowAge"
                      value={newGiveaway.restrictions.minFollowAge}
                      onChange={handleChange}
                      className="input w-full"
                      min={0}
                      max={365}
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 mb-2" htmlFor="restrictions.minAccountAge">
                      Min Account Age (days)
                    </label>
                    <input
                      type="number"
                      name="restrictions.minAccountAge"
                      value={newGiveaway.restrictions.minAccountAge}
                      onChange={handleChange}
                      className="input w-full"
                      min={0}
                      max={365}
                    />
                  </div>

                  <div>
                    <label className="block text-gray-300 mb-2" htmlFor="restrictions.maxEntries">
                      Max Entries
                    </label>
                    <input
                      type="number"
                      name="restrictions.maxEntries"
                      value={newGiveaway.restrictions.maxEntries}
                      onChange={handleChange}
                      className="input w-full"
                      min={1}
                      max={10000}
                    />
                  </div>
                </div>

                <div className="mt-4">
                  <label className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      name="restrictions.allowMultipleEntries"
                      checked={newGiveaway.restrictions.allowMultipleEntries}
                      onChange={handleChange}
                      className="w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500"
                    />
                    <span>Allow Multiple Entries per User</span>
                  </label>
                </div>
              </div>
            )}

            {/* Announcement Settings */}
            <div className="border-t border-gray-700 pt-6">
              <h3 className="text-lg font-bold mb-4 flex items-center space-x-2">
                <MessageSquare className="w-5 h-5 text-green-400" />
                <span>Announcement Messages</span>
              </h3>

              <div className="space-y-4">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="announceStart"
                    name="announceStart"
                    checked={newGiveaway.announceStart}
                    onChange={handleChange}
                    className="w-4 h-4 text-green-600 bg-gray-700 border-gray-600 rounded focus:ring-green-500"
                  />
                  <label htmlFor="announceStart" className="text-gray-300">
                    Send announcement when giveaway starts
                  </label>
                </div>

                {newGiveaway.announceStart && (
                  <div className="grid grid-cols-1 gap-4">
                    <div>
                      <label className="block text-gray-300 mb-2" htmlFor="startMessage">
                        Start Announcement
                      </label>
                      <textarea
                        id="startMessage"
                        name="startMessage"
                        value={newGiveaway.startMessage}
                        onChange={handleChange}
                        className="input w-full h-20 resize-none"
                        placeholder="🎉 {title} giveaway has started! Type {keyword} to enter and win {prize}! 🎉"
                      />
                    </div>

                    <div>
                      <label className="block text-gray-300 mb-2" htmlFor="winnerMessage">
                        Winner Announcement
                      </label>
                      <textarea
                        id="winnerMessage"
                        name="winnerMessage"
                        value={newGiveaway.winnerMessage}
                        onChange={handleChange}
                        className="input w-full h-20 resize-none"
                        placeholder="Congrats {winner}, you just won {prize}!"
                      />
                    </div>
                  </div>
                )}

                {newGiveaway.announceStart && (
                  <p className="text-xs text-gray-400">
                    Available placeholders: <code>{'{title}'}</code>, <code>{'{prize}'}</code>, <code>{'{keyword}'}</code>, <code>{'{winner}'}</code>
                  </p>
                )}
              </div>
            </div>

            <div className="flex justify-end pt-4">
              <button type="submit" className="btn btn-primary flex items-center space-x-2 text-lg px-8 py-3">
                <Trophy size={20} />
                <span>Start Giveaway</span>
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Enhanced User Profile Modal */}
      <UserProfileModal
        isOpen={showUserModal || isWinnerModal}
        onClose={isWinnerModal ? closeWinnerModal : closeUserModal}
        userProfile={userProfile}
        onRefresh={getUserProfile}
        isWinnerModal={isWinnerModal}
        onSelectWinner={isWinnerModal ? confirmWinner : undefined}
        onReroll={isWinnerModal ? rerollWinner : undefined}
      />
    </div>
  );
};

export default Giveaways;