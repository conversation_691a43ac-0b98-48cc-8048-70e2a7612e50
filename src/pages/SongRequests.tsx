import { useState, useEffect } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { useSongRequests } from '../hooks/useSongRequests';
import {
  Music,
  PlayCircle,
  SkipForward,
  Volume2,
  Trash2,
  GripVertical,
  Pause,
  Search,
  AlertCircle,
  CheckCircle,
  Monitor
} from 'lucide-react';

const SongRequests = () => {
  const {
    queue,
    currentSong,
    isPlaying,
    volume,
    searchResults,
    addSong,
    removeSong,
    playSong,
    pauseSong,
    skipSong,
    setVolume,
    reorderQueue,
    searchSongs
  } = useSongRequests();

  const [searchTerm, setSearchTerm] = useState('');
  const [notification, setNotification] = useState<{ type: 'success' | 'error'; message: string } | null>(null);
  const [expandedVideoPlayer, setExpandedVideoPlayer] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [maxSongsPerUser, setMaxSongsPerUser] = useState(3);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      searchSongs(searchTerm);
    }
  };

  const handleDragEnd = (result: any) => {
    if (!result.destination) return;
    reorderQueue(result.source.index, result.destination.index);
  };

  const formatDuration = (seconds: number) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const showNotification = (type: 'success' | 'error', message: string) => {
    setNotification({ type, message });
    setTimeout(() => setNotification(null), 5000);
  };

  const handleAddSong = (song: any) => {
    const songWithUser = {
      ...song,
      requestedBy: 'StreamerName' // In real app, this would be the current user
    };
    addSong(songWithUser);
  };

  const getVideoId = (song: any) => {
    if (song.sourceId) return song.sourceId;
    if (song.url) {
      // Handle YouTube and Piped URLs
      const urlMatch = song.url.match(/(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|piped\.video\/watch\?v=|piped\.video\/embed\/)([^&\n?#]+)/);
      return urlMatch ? urlMatch[1] : null;
    }
    return null;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold">Song Requests</h1>
          <p className="text-gray-400">Manage music requests from your viewers</p>
        </div>
        <button
          onClick={() => setShowSettings(!showSettings)}
          className="btn btn-secondary"
        >
          Settings
        </button>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="card">
          <h3 className="text-lg font-semibold mb-4">Song Request Settings</h3>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <label htmlFor="allow-requests" className="text-gray-300">
                Allow Song Requests
              </label>
              <input
                type="checkbox"
                id="allow-requests"
                defaultChecked={true}
                className="h-4 w-4 rounded border-gray-600 text-purple-600 focus:ring-purple-500 bg-gray-700"
              />
            </div>
            <div>
              <label htmlFor="request-command" className="block text-gray-300 text-sm mb-1">
                Request Command
              </label>
              <input
                type="text"
                id="request-command"
                defaultValue="!sr"
                className="input w-full"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Max Songs Per User
              </label>
              <input
                type="number"
                min="1"
                max="10"
                value={maxSongsPerUser}
                onChange={(e) => setMaxSongsPerUser(Number(e.target.value))}
                className="input w-32"
              />
              <p className="text-xs text-gray-400 mt-1">
                Maximum number of songs each user can have in the queue
              </p>
            </div>
            <div>
              <label htmlFor="max-duration" className="block text-gray-300 text-sm mb-1">
                Max Song Duration (minutes)
              </label>
              <input
                type="number"
                id="max-duration"
                defaultValue={10}
                min={1}
                className="input w-full"
              />
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => {
                  // TODO: Save settings to server
                  showNotification('success', `Settings saved successfully`);
                  setShowSettings(false);
                }}
                className="btn btn-primary"
              >
                Save Settings
              </button>
              <button
                onClick={() => setShowSettings(false)}
                className="btn btn-secondary"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Notification */}
      {notification && (
        <div className={`flex items-center space-x-2 p-4 rounded-lg ${
          notification.type === 'success'
            ? 'bg-green-900 border border-green-700 text-green-100'
            : 'bg-red-900 border border-red-700 text-red-100'
        }`}>
          {notification.type === 'success' ? (
            <CheckCircle className="w-5 h-5" />
          ) : (
            <AlertCircle className="w-5 h-5" />
          )}
          <span>{notification.message}</span>
        </div>
      )}

      {/* Video Player Section - Always visible when there's a current song */}
      {currentSong && (
        <div className="card">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold flex items-center space-x-2">
              <Monitor className="w-5 h-5" />
              <span>Video Player</span>
            </h2>
            <button
              onClick={() => setExpandedVideoPlayer(!expandedVideoPlayer)}
              className={`text-gray-400 hover:text-white ${expandedVideoPlayer ? 'text-purple-400' : ''}`}
              title={expandedVideoPlayer ? 'Minimize Video' : 'Expand Video'}
            >
              {expandedVideoPlayer ? '⬇' : '⬆'}
            </button>
          </div>
          <div className={`bg-black rounded-lg overflow-hidden ${
            expandedVideoPlayer ? 'aspect-video' : 'h-20'
          }`}>
            {getVideoId(currentSong) ? (
              <iframe
                width="100%"
                height="100%"
                src={`https://piped.video/embed/${getVideoId(currentSong)}?autoplay=1&controls=${expandedVideoPlayer ? '1' : '0'}`}
                title={currentSong.title}
                frameBorder="0"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
                allowFullScreen
              />
            ) : (
              <div className="flex items-center justify-center h-full text-gray-400">
                <p>Video not available</p>
              </div>
            )}
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2">
          <div className="card">
            <h2 className="text-xl font-bold mb-4">Queue</h2>

            {currentSong && (
              <div className="bg-gray-700 rounded-lg p-4 mb-6">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-bold">Now Playing</h3>
                  <div className="flex items-center space-x-2">
                    <button
                      className="p-2 rounded-full hover:bg-gray-600"
                      onClick={isPlaying ? pauseSong : playSong}
                    >
                      {isPlaying ? <Pause size={20} /> : <PlayCircle size={20} />}
                    </button>
                    <button
                      className="p-2 rounded-full hover:bg-gray-600"
                      onClick={skipSong}
                    >
                      <SkipForward size={20} />
                    </button>
                    <button
                      className={`p-2 rounded-full hover:bg-gray-600 ${expandedVideoPlayer ? 'text-purple-400' : ''}`}
                      onClick={() => setExpandedVideoPlayer(!expandedVideoPlayer)}
                      title={expandedVideoPlayer ? 'Minimize Video' : 'Expand Video'}
                    >
                      <Monitor size={20} />
                    </button>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="h-16 w-16 rounded bg-gray-800 flex-shrink-0 overflow-hidden">
                    {currentSong.thumbnail && (
                      <img
                        src={currentSong.thumbnail}
                        alt={currentSong.title}
                        className="h-full w-full object-cover"
                      />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h4 className="font-bold truncate">{currentSong.title}</h4>
                    <div className="flex items-center space-x-2 mt-1">
                      <p className="text-xs text-gray-400">
                        Requested by: {currentSong.requestedBy}
                      </p>
                      <p className="text-xs text-gray-400">
                        Duration: {formatDuration(currentSong.duration)}
                      </p>
                    </div>
                  </div>
                </div>
                <div className="mt-3">
                  <div className="flex items-center space-x-3">
                    <Volume2 size={16} className="text-gray-400" />
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={volume}
                      onChange={(e) => setVolume(Number(e.target.value))}
                      className="flex-1 h-2 bg-gray-600 rounded-lg appearance-none cursor-pointer"
                    />
                    <span className="text-sm text-gray-400">{volume}%</span>
                  </div>
                  <div className="h-1 bg-gray-600 rounded-full mt-2 overflow-hidden">
                    <div
                      className="h-full bg-purple-600"
                      style={{ width: `${(currentSong.currentTime / currentSong.duration) * 100}%` }}
                    ></div>
                  </div>
                  <div className="flex justify-between text-xs text-gray-400 mt-1">
                    <span>{formatDuration(currentSong.currentTime)}</span>
                    <span>{formatDuration(currentSong.duration)}</span>
                  </div>
                </div>
              </div>
            )}

            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="songs">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    className="space-y-2"
                  >
                    {queue.length > 0 ? (
                      queue.map((song, index) => (
                        <Draggable key={song.id} draggableId={song.id} index={index}>
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              className="flex items-center space-x-3 bg-gray-700 p-3 rounded-lg"
                            >
                              <div {...provided.dragHandleProps} className="cursor-grab">
                                <GripVertical size={20} className="text-gray-400" />
                              </div>
                              <div className="h-12 w-12 rounded bg-gray-800 flex-shrink-0 overflow-hidden">
                                {song.thumbnail && (
                                  <img
                                    src={song.thumbnail}
                                    alt={song.title}
                                    className="h-full w-full object-cover"
                                  />
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <h4 className="font-medium truncate">{song.title}</h4>
                                <div className="flex items-center space-x-2">
                                  <p className="text-gray-400 text-xs">{formatDuration(song.duration)}</p>
                                </div>
                              </div>
                              <div className="text-xs text-gray-400">
                                {song.requestedBy}
                              </div>
                              <button
                                className="p-2 text-gray-400 hover:text-red-500 rounded hover:bg-gray-600"
                                onClick={() => removeSong(song.id)}
                              >
                                <Trash2 size={18} />
                              </button>
                              <button
                                className="p-2 text-gray-400 hover:text-white rounded hover:bg-gray-600"
                                onClick={() => playSong(song.id)}
                              >
                                <PlayCircle size={18} />
                              </button>
                            </div>
                          )}
                        </Draggable>
                      ))
                    ) : (
                      <div className="text-center py-8 text-gray-400">
                        <p>The queue is empty. Add songs or wait for viewer requests.</p>
                      </div>
                    )}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>
        </div>

        <div>
          <div className="card">
            <h2 className="text-xl font-bold mb-4">Add Songs</h2>

            <form onSubmit={handleSearch} className="mb-4">
              <div className="relative">
                <input
                  type="text"
                  value={searchTerm}
                  onChange={handleSearchChange}
                  placeholder="Search for songs on YouTube..."
                  className="input w-full pr-10"
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white"
                >
                  <Search size={20} />
                </button>
              </div>
            </form>

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {searchResults.length > 0 ? (
                searchResults.map((song) => (
                  <div key={song.id} className="flex items-center space-x-3 bg-gray-700 p-3 rounded-lg">
                    <div className="h-10 w-10 rounded bg-gray-800 flex-shrink-0 overflow-hidden">
                      {song.thumbnail && (
                        <img
                          src={song.thumbnail}
                          alt={song.title}
                          className="h-full w-full object-cover"
                        />
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm truncate">{song.title}</h4>
                    </div>
                    <button
                      className="p-2 text-gray-400 hover:text-white rounded hover:bg-gray-600"
                      onClick={() => handleAddSong(song)}
                      title="Add to Queue"
                    >
                      <PlayCircle size={18} />
                    </button>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-gray-400">
                  <p>Search for songs to add to the queue</p>
                </div>
              )}
            </div>


          </div>
        </div>
      </div>
    </div>
  );
};

export default SongRequests;