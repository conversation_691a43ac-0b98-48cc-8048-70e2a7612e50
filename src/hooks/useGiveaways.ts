import { useState, useEffect } from 'react';
import { useSocket } from './useSocket';
import { nanoid } from 'nanoid';

type GiveawayEntry = {
  username: string;
  displayName?: string;
  timestamp: string;
  message: string;
  userType: 'viewer' | 'follower' | 'subscriber' | 'vip' | 'moderator';
  badges: string[];
  isEligible: boolean;
  userId?: string;
  followDate?: string;
  subTier?: number;
};

type GiveawayRestrictions = {
  subscribersOnly: boolean;
  followersOnly: boolean;
  excludeModerators: boolean;
  excludeVips: boolean;
  minFollowAge: number; // in days
  minAccountAge: number; // in days
  maxEntries: number;
  allowMultipleEntries: boolean;
};

type ActiveGiveaway = {
  id: string;
  title: string;
  prize: string;
  keyword: string;
  startTime: string;
  endTime: string;
  timeRemaining: string;
  isPaused: boolean;
  entries: GiveawayEntry[];
  eligibleEntries: GiveawayEntry[];
  winner?: GiveawayEntry;
  restrictions: GiveawayRestrictions;
  announceStart?: boolean;
  startMessage?: string;
  winnerMessage?: string;
};

type UserProfile = {
  username: string;
  displayName?: string;
  messages: any[];
  stats: {
    totalMessages: number;
    firstSeen: string;
    lastSeen: string;
    isMod: boolean;
    isSubscriber: boolean;
    isVip: boolean;
    isFollower: boolean;
    accountAge?: string;
    followDate?: string;
    subTier?: number;
    subStreak?: number;
  };
  badges?: string[];
  recentActivity?: {
    lastMessage: string;
    messageFrequency: number;
    avgWordsPerMessage: number;
  };
};



export const useGiveaways = () => {
  const socket = useSocket();
  const [activeGiveaway, setActiveGiveaway] = useState<ActiveGiveaway | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [showUserModal, setShowUserModal] = useState(false);
  const [isWinnerModal, setIsWinnerModal] = useState(false);
  const [selectedWinner, setSelectedWinner] = useState<GiveawayEntry | null>(null);

  useEffect(() => {
    if (!socket) return;

    socket.on('giveawayUpdate', (data: { active: ActiveGiveaway | null }) => {
      setActiveGiveaway(data.active);
    });

    socket.on('winnerDrawn', (winner: GiveawayEntry) => {
      console.log('Winner drawn:', winner);
      setSelectedWinner(winner);
      setIsWinnerModal(true);
      // Get winner's profile automatically
      getUserProfile(winner.username);
    });

    socket.on('userProfile', (profile: UserProfile) => {
      setUserProfile(profile);
      if (!isWinnerModal) {
        setShowUserModal(true);
      }
    });

    socket.on('giveawayEntryAdded', (entry: GiveawayEntry) => {
      console.log('New giveaway entry:', entry);
      // Update active giveaway with new entry
      setActiveGiveaway(prev => {
        if (!prev) return null;
        return {
          ...prev,
          entries: [...prev.entries, entry],
          eligibleEntries: entry.isEligible
            ? [...prev.eligibleEntries, entry]
            : prev.eligibleEntries
        };
      });
    });

    socket.on('giveawayEntryRejected', (data: { username: string; reason: string }) => {
      console.log('Entry rejected:', data);
      // Could show a notification here
    });

    // Get initial giveaway data
    socket.emit('getGiveaways');

    // Request updates every 3 seconds
    const interval = setInterval(() => {
      socket.emit('getGiveaways');
    }, 3000);

    return () => {
      socket.off('giveawayUpdate');
      socket.off('winnerDrawn');
      socket.off('userProfile');
      socket.off('giveawayEntryAdded');
      socket.off('giveawayEntryRejected');
      clearInterval(interval);
    };
  }, [socket, isWinnerModal]);

  const startGiveaway = (giveaway: any) => {
    if (socket) {
      socket.emit('startGiveaway', giveaway);
    }
  };

  const endGiveaway = () => {
    if (socket) {
      socket.emit('endGiveaway');
    }
  };

  const drawWinner = () => {
    console.log('🎲 Frontend: Drawing winner...');
    if (socket && activeGiveaway) {
      console.log('📡 Frontend: Emitting drawWinner event');
      socket.emit('drawWinner', { giveawayId: activeGiveaway.id });
    } else {
      console.log('❌ Frontend: No socket connection or active giveaway');
    }
  };

  const confirmWinner = () => {
    if (socket && selectedWinner && activeGiveaway) {
      socket.emit('confirmWinner', {
        giveawayId: activeGiveaway.id,
        winner: selectedWinner
      });
      setIsWinnerModal(false);
      setSelectedWinner(null);
    }
  };

  const rerollWinner = () => {
    if (socket && activeGiveaway) {
      socket.emit('rerollWinner', { giveawayId: activeGiveaway.id });
      setIsWinnerModal(false);
      setSelectedWinner(null);
    }
  };

  const resetGiveaway = () => {
    if (socket) {
      socket.emit('resetGiveaway');
    }
  };

  const getUserProfile = (username: string) => {
    if (socket) {
      socket.emit('getUserProfile', username);
    }
  };

  const closeUserModal = () => {
    setShowUserModal(false);
    if (!isWinnerModal) {
      setUserProfile(null);
    }
  };

  const closeWinnerModal = () => {
    setIsWinnerModal(false);
    setSelectedWinner(null);
    setUserProfile(null);
    setShowUserModal(false);
  };

  const viewUserProfile = (username: string) => {
    setIsWinnerModal(false);
    getUserProfile(username);
  };

  return {
    activeGiveaway,
    userProfile,
    showUserModal,
    isWinnerModal,
    selectedWinner,
    startGiveaway,
    endGiveaway,
    drawWinner,
    confirmWinner,
    rerollWinner,
    resetGiveaway,
    getUserProfile,
    viewUserProfile,
    closeUserModal,
    closeWinnerModal
  };
};