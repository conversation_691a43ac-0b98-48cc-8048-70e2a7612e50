import { useEffect, useState } from 'react';
import { X } from 'lucide-react';
import PipedPlayer from './PipedPlayer';

interface VideoPopupProps {
  isOpen: boolean;
  onClose: () => void;
  videoId: string;
  title: string;
  duration: number;
  requestedBy: string;
  songInstanceId?: string;
  onSongEnd: () => void;
}

const VideoPopup: React.FC<VideoPopupProps> = ({
  isOpen,
  onClose,
  videoId,
  title,
  duration,
  requestedBy,
  songInstanceId,
  onSongEnd
}) => {
  const [isAnimating, setIsAnimating] = useState(false);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when popup is open
      document.body.style.overflow = 'hidden';
      // Trigger entrance animation
      setIsAnimating(true);
    } else {
      setIsAnimating(false);
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      {/* Backdrop */}
      <div
        className={`absolute inset-0 bg-black backdrop-blur-md transition-all duration-300 ${
          isAnimating ? 'bg-opacity-80' : 'bg-opacity-0'
        }`}
        onClick={onClose}
      />

      {/* Popup Content */}
      <div className={`relative w-full max-w-6xl bg-gray-900 rounded-2xl shadow-2xl overflow-hidden border border-gray-700 transform transition-all duration-300 ${
        isAnimating ? 'scale-100 opacity-100 translate-y-0' : 'scale-95 opacity-0 translate-y-4'
      }`}>
        {/* Header */}
        <div className="flex items-center justify-between p-6 bg-gradient-to-r from-gray-800 to-gray-900 border-b border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="w-3 h-3 bg-red-500 rounded-full"></div>
            <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
            <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            <div className="ml-4 flex-1">
              <h2 className="text-xl font-bold text-white truncate pr-4 max-w-md"
                  style={{
                    display: '-webkit-box',
                    WebkitLineClamp: 1,
                    WebkitBoxOrient: 'vertical' as const,
                    overflow: 'hidden'
                  }}>
                {title}
              </h2>
              <p className="text-sm text-gray-400 mt-1">Video Player</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-3 text-gray-400 hover:text-white rounded-xl hover:bg-gray-700 transition-all duration-200 hover:scale-105"
            title="Close video (ESC)"
          >
            <X size={24} />
          </button>
        </div>

        {/* Video Player */}
        <div className="relative bg-black">
          <div className="aspect-video">
            <PipedPlayer
              videoId={videoId}
              audioOnly={false} // Video mode for popup
              title={title}
              duration={duration}
              requestedBy={requestedBy}
              songInstanceId={songInstanceId}
              onSongEnd={onSongEnd}
              className="w-full h-full"
            />
          </div>

          {/* Video Controls Overlay */}
          <div className="absolute bottom-4 right-4 flex space-x-2">
            <div className="bg-black bg-opacity-60 backdrop-blur-sm rounded-lg px-3 py-2 text-white text-sm font-mono">
              Playing in Video Mode
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 bg-gradient-to-r from-gray-800 to-gray-900 border-t border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-400">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                <span>Live Audio & Video</span>
              </div>
              <div className="text-sm text-gray-500">•</div>
              <div className="text-sm text-gray-400">
                Powered by Piped
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <p className="text-sm text-gray-400">
                Press <kbd className="px-3 py-1 bg-gray-700 rounded-md text-xs font-mono border border-gray-600">ESC</kbd> or click outside to close
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default VideoPopup;
