import { useEffect } from 'react';
import { X } from 'lucide-react';
import PipedPlayer from './PipedPlayer';

interface VideoPopupProps {
  isOpen: boolean;
  onClose: () => void;
  videoId: string;
  title: string;
  duration: number;
  onSongEnd: () => void;
}

const VideoPopup: React.FC<VideoPopupProps> = ({
  isOpen,
  onClose,
  videoId,
  title,
  duration,
  onSongEnd
}) => {
  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);
      // Prevent body scroll when popup is open
      document.body.style.overflow = 'hidden';
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-75 backdrop-blur-sm"
        onClick={onClose}
      />
      
      {/* Popup Content */}
      <div className="relative w-full max-w-4xl mx-4 bg-gray-900 rounded-lg shadow-2xl overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 bg-gray-800 border-b border-gray-700">
          <h2 className="text-lg font-semibold text-white truncate pr-4">
            {title}
          </h2>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-white rounded-full hover:bg-gray-700 transition-colors"
            title="Close video (ESC)"
          >
            <X size={20} />
          </button>
        </div>
        
        {/* Video Player */}
        <div className="aspect-video bg-black">
          <PipedPlayer
            videoId={videoId}
            audioOnly={false} // Video mode for popup
            title={title}
            duration={duration}
            onSongEnd={onSongEnd}
            className="w-full h-full"
          />
        </div>
        
        {/* Footer */}
        <div className="p-4 bg-gray-800 text-center">
          <p className="text-sm text-gray-400">
            Press <kbd className="px-2 py-1 bg-gray-700 rounded text-xs">ESC</kbd> or click outside to close
          </p>
        </div>
      </div>
    </div>
  );
};

export default VideoPopup;
