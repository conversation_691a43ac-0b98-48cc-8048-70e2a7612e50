import { useEffect, useRef, useState, useCallback } from 'react';

interface PipedPlayerProps {
  videoId: string;
  autoplay?: boolean;
  controls?: boolean;
  audioOnly?: boolean; // New prop for audio-only mode
  title?: string;
  className?: string;
  duration?: number; // Expected duration in seconds
  requestedBy?: string; // Who requested the song
  songInstanceId?: string; // Unique ID for this song instance to prevent cache conflicts
  onSongEnd?: (songId?: string) => void; // Callback when song finishes, with optional song ID
}

interface PipedVideoData {
  videoStreams: Array<{
    url: string;
    quality: string;
    mimeType: string;
  }>;
  audioStreams: Array<{
    url: string;
    quality: string;
    mimeType: string;
  }>;
  title: string;
  uploader: string;
}

// Global state to persist video data and playback state
const videoDataCache = new Map<string, PipedVideoData>();
const playbackStateCache = new Map<string, { currentTime: number; volume: number }>();

// Persistent storage keys
const STORAGE_KEY_PREFIX = 'piped_player_';
const CURRENT_SONG_KEY = 'current_song_state';

// Helper functions for localStorage persistence
const savePlaybackState = (videoId: string, currentTime: number, volume: number, instanceId?: string) => {
  try {
    const state = { currentTime, volume, timestamp: Date.now(), instanceId };
    localStorage.setItem(`${STORAGE_KEY_PREFIX}${videoId}`, JSON.stringify(state));
    localStorage.setItem(CURRENT_SONG_KEY, JSON.stringify({ videoId, ...state }));
  } catch (error) {
    console.log('Could not save playback state:', error);
  }
};

const getCurrentSongState = () => {
  try {
    const stored = localStorage.getItem(CURRENT_SONG_KEY);
    if (stored) {
      const state = JSON.parse(stored);
      // Only restore if the state is less than 10 minutes old (for refresh scenarios)
      if (Date.now() - state.timestamp < 600000) {
        return state;
      }
    }
  } catch (error) {
    console.log('Could not load current song state:', error);
  }
  return null;
};

// Clean up old localStorage entries
const cleanupOldStates = () => {
  try {
    const keys = Object.keys(localStorage);
    const now = Date.now();

    keys.forEach(key => {
      if (key.startsWith(STORAGE_KEY_PREFIX)) {
        try {
          const stored = localStorage.getItem(key);
          if (stored) {
            const state = JSON.parse(stored);
            // Remove states older than 24 hours
            if (now - state.timestamp > 86400000) {
              localStorage.removeItem(key);
            }
          }
        } catch (error) {
          // Remove corrupted entries
          localStorage.removeItem(key);
        }
      }
    });
  } catch (error) {
    console.log('Could not cleanup old states:', error);
  }
};

const PipedPlayer: React.FC<PipedPlayerProps> = ({
  videoId,
  autoplay = true,
  controls = true, // Keep for interface compatibility but always use false
  audioOnly = true, // Default to audio-only mode
  title = 'Video Player',
  className = '',
  duration = 0,
  requestedBy = 'Unknown',
  songInstanceId,
  onSongEnd
}) => {
  // Suppress unused variable warning - controls kept for interface compatibility
  void controls;

  // Create unique cache key for this song instance
  const cacheKey = songInstanceId || `${videoId}_${Date.now()}`;

  // Helper function to format duration
  const formatDuration = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = Math.floor(seconds % 60);
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };
  const videoRef = useRef<HTMLVideoElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const autoSkipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [useDirectStream, setUseDirectStream] = useState(false);
  const [videoData, setVideoData] = useState<PipedVideoData | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [hasRestoredPosition, setHasRestoredPosition] = useState(false);
  const [lastSongInstanceId, setLastSongInstanceId] = useState<string | null>(null);

  const PIPED_FRONTEND = 'https://pipedapi.orangenet.cc'; // Custom Piped frontend instance
  const PIPED_API = 'https://pipedapi.orangenet.cc'; // Custom Piped API instance

  useEffect(() => {
    // Set the Piped instance in the parent window's localStorage
    try {
      localStorage.setItem('instance', PIPED_API);
      localStorage.setItem('pipedInstance', PIPED_API);
      console.log('Piped API instance set to:', PIPED_API);
    } catch (error) {
      console.log('Could not set Piped instance in localStorage:', error);
    }

    // Clean up old states on component mount
    cleanupOldStates();
  }, []);

  // Reset currentTime when a new song instance starts
  useEffect(() => {
    if (songInstanceId && songInstanceId !== lastSongInstanceId) {
      console.log(`New song instance detected: ${songInstanceId} (previous: ${lastSongInstanceId})`);
      setCurrentTime(0);
      setLastSongInstanceId(songInstanceId);
      setHasRestoredPosition(false);

      // Clear any existing auto-skip timeout for the previous song
      if (autoSkipTimeoutRef.current) {
        clearTimeout(autoSkipTimeoutRef.current);
        autoSkipTimeoutRef.current = null;
      }
    }
  }, [songInstanceId, lastSongInstanceId]);

  // Initialize for first song instance
  useEffect(() => {
    if (songInstanceId && !lastSongInstanceId) {
      console.log(`First song instance: ${songInstanceId}`);
      setCurrentTime(0);
      setLastSongInstanceId(songInstanceId);
    }
  }, [songInstanceId, lastSongInstanceId]);

  // Restore playback position on component mount (only for refresh scenarios, not duplicates)
  useEffect(() => {
    if (!hasRestoredPosition && videoId && songInstanceId) {
      const currentSongState = getCurrentSongState();
      // Only restore if this is the exact same song instance (from refresh)
      if (currentSongState && currentSongState.videoId === videoId && currentSongState.instanceId === songInstanceId) {
        console.log('Restoring playback position from refresh:', currentSongState.currentTime);
        setCurrentTime(currentSongState.currentTime);
        setHasRestoredPosition(true);
      } else {
        console.log('Not restoring state - this is a new instance of the song');
        setHasRestoredPosition(true);
      }
    } else if (!songInstanceId) {
      // Fallback for songs without instance ID - don't restore to prevent duplicate issues
      console.log('No songInstanceId provided - starting fresh to prevent duplicate song issues');
      setCurrentTime(0);
      setHasRestoredPosition(true);
    }
  }, [videoId, songInstanceId, hasRestoredPosition]);

  // Auto-skip functionality
  const handleAutoSkip = useCallback(() => {
    if (duration > 0 && onSongEnd) {
      // Calculate remaining time based on current position
      const remainingTime = Math.max(0, duration - currentTime);
      const skipTime = (remainingTime + 5) * 1000; // remaining time + 5 seconds in milliseconds
      console.log(`Setting auto-skip for ${skipTime}ms (remaining: ${remainingTime}s) for song ID: ${songInstanceId}`);

      autoSkipTimeoutRef.current = setTimeout(() => {
        console.log(`Auto-skipping song with ID: ${songInstanceId}`);
        onSongEnd(songInstanceId);
      }, skipTime);
    }
  }, [duration, currentTime, onSongEnd, songInstanceId]);

  // Clear auto-skip timeout
  const clearAutoSkip = useCallback(() => {
    if (autoSkipTimeoutRef.current) {
      clearTimeout(autoSkipTimeoutRef.current);
      autoSkipTimeoutRef.current = null;
    }
  }, []);

  // Try to fetch video data directly from Piped API
  useEffect(() => {
    const fetchVideoData = async () => {
      if (!videoId) return;

      // Check cache first
      const cachedData = videoDataCache.get(videoId);
      if (cachedData) {
        setVideoData(cachedData);
        setUseDirectStream(true);
        setLoading(false);
        console.log('Using cached video data for:', videoId);
        return;
      }

      setLoading(true);
      try {
        console.log('Attempting to fetch video data from Piped API...');
        const response = await fetch(`${PIPED_API}/streams/${videoId}`);

        if (response.ok) {
          const data: PipedVideoData = await response.json();
          // Cache the data
          videoDataCache.set(videoId, data);
          setVideoData(data);
          setUseDirectStream(true);
          console.log('Successfully fetched video data from Piped API:', data.title);
        } else {
          console.log('Piped API not available, falling back to iframe embed');
          setUseDirectStream(false);
        }
      } catch (error) {
        console.log('Error fetching from Piped API, using iframe fallback:', error);
        setUseDirectStream(false);
      } finally {
        setLoading(false);
      }
    };

    fetchVideoData();
  }, [videoId, PIPED_API]);

  // Setup video/audio element when it loads
  useEffect(() => {
    const mediaElement = videoRef.current;
    if (!mediaElement || !useDirectStream) return;

    const handleLoadedMetadata = () => {
      // Set volume to maximum
      mediaElement.volume = 1.0;

      // Only restore playback state if this is the same song instance (from refresh)
      if (hasRestoredPosition && currentTime > 0 && songInstanceId) {
        const currentSongState = getCurrentSongState();
        // Double-check this is the exact same instance from a refresh
        if (currentSongState && currentSongState.instanceId === songInstanceId && currentSongState.videoId === videoId) {
          mediaElement.currentTime = currentTime;
          console.log('Restored from refresh at time:', currentTime, 'for instance:', songInstanceId);
        } else {
          // Even if we have currentTime, if it's not from a refresh, start fresh
          mediaElement.currentTime = 0;
          setCurrentTime(0);
          console.log('Starting fresh - not a refresh scenario for instance:', songInstanceId);
        }
      } else {
        // Start fresh for new instances (including duplicates)
        console.log('Starting fresh playback for new song instance:', songInstanceId || 'no-id');
        mediaElement.currentTime = 0;
        setCurrentTime(0);
      }

      // Start auto-skip timer
      handleAutoSkip();
    };

    const handleTimeUpdate = () => {
      const time = mediaElement.currentTime;
      setCurrentTime(time);

      // Save playback state to both memory and localStorage with instance ID
      const state = {
        currentTime: time,
        volume: mediaElement.volume
      };
      playbackStateCache.set(cacheKey, state);
      savePlaybackState(videoId, time, mediaElement.volume, songInstanceId);
    };

    const handleEnded = () => {
      console.log(`Video ended naturally for song ID: ${songInstanceId}`);
      clearAutoSkip();
      if (onSongEnd) {
        onSongEnd(songInstanceId);
      }
    };

    const handleError = () => {
      console.log(`Video playback error for song ID: ${songInstanceId}, skipping...`);
      clearAutoSkip();
      if (onSongEnd) {
        onSongEnd(songInstanceId);
      }
    };

    mediaElement.addEventListener('loadedmetadata', handleLoadedMetadata);
    mediaElement.addEventListener('timeupdate', handleTimeUpdate);
    mediaElement.addEventListener('ended', handleEnded);
    mediaElement.addEventListener('error', handleError);

    return () => {
      mediaElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
      mediaElement.removeEventListener('timeupdate', handleTimeUpdate);
      mediaElement.removeEventListener('ended', handleEnded);
      mediaElement.removeEventListener('error', handleError);
    };
  }, [videoId, useDirectStream, handleAutoSkip, clearAutoSkip, onSongEnd]);

  // For iframe fallback, set up auto-skip (only when video is requested)
  useEffect(() => {
    if (!useDirectStream && !loading && !audioOnly) {
      handleAutoSkip();
      return () => clearAutoSkip();
    }
  }, [useDirectStream, loading, audioOnly, handleAutoSkip, clearAutoSkip]);

  // For audio-only mode without iframe, set up auto-skip timer
  useEffect(() => {
    if (!useDirectStream && !loading && audioOnly) {
      console.log('Setting up audio-only auto-skip timer');
      handleAutoSkip();
      return () => clearAutoSkip();
    }
  }, [useDirectStream, loading, audioOnly, handleAutoSkip, clearAutoSkip]);

  // Cleanup on unmount or video change
  useEffect(() => {
    return () => {
      clearAutoSkip();
    };
  }, [videoId, clearAutoSkip]);

  // Render logic - all hooks are called above this point
  const embedUrl = `${PIPED_FRONTEND}/embed/${videoId}?autoplay=${autoplay ? 1 : 0}&controls=0`; // Remove controls

  // If we have direct stream data, use HTML5 audio/video player
  if (useDirectStream && videoData && !loading) {
    const audioStream = videoData.audioStreams.find(s => s.mimeType.includes('mp4')) || videoData.audioStreams[0];
    const videoStream = videoData.videoStreams.find(s => s.quality === '720p') ||
      videoData.videoStreams.find(s => s.quality === '480p') ||
      videoData.videoStreams[0];

    const mediaProps = {
      ref: videoRef,
      width: "100%",
      height: "100%",
      controls: false, // Remove controls to hide duration/volume bars
      autoPlay: autoplay,
      className: "w-full h-full",
      crossOrigin: "anonymous" as const,
      style: {
        backgroundColor: '#000',
        objectFit: 'contain' as const
      }
    };

    return (
      <div className={`relative ${className}`}>
        {audioOnly ? (
          // Always use audio-only mode
          <audio {...mediaProps}>
            <source src={audioStream.url} type={audioStream.mimeType} />
            Your browser does not support the audio tag.
          </audio>
        ) : (
          // Video mode for popup
          <video {...mediaProps}>
            <source src={videoStream.url} type={videoStream.mimeType} />
            <source src={audioStream.url} type={audioStream.mimeType} />
            Your browser does not support the video tag.
          </video>
        )}

        {/* Audio-only overlay */}
        {audioOnly && (
            <div className="text-center max-w-full">
            </div>
        )}

        {/* Video mode overlay */}
        {!audioOnly && (
          <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white text-sm px-2 py-1 rounded">
            {Math.floor(currentTime / 60)}:{(Math.floor(currentTime % 60)).toString().padStart(2, '0')} / {Math.floor(duration / 60)}:{(Math.floor(duration % 60)).toString().padStart(2, '0')}
          </div>
        )}
      </div>
    );
  }

  // Fallback to iframe embed - only when video is requested
  if (!audioOnly) {
    return (
      <div className={`relative ${className}`}>
        <iframe
          ref={iframeRef}
          width="100%"
          height="100%"
          src={embedUrl}
          title={title}
          style={{ border: 0 }}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          onLoad={() => {
            console.log('Piped iframe loaded for video:', videoId, 'using instance:', PIPED_FRONTEND);

            try {
              if (iframeRef.current?.contentWindow) {
                iframeRef.current.contentWindow.postMessage({
                  type: 'SET_INSTANCE',
                  instance: PIPED_API
                }, PIPED_FRONTEND);

                setTimeout(() => {
                  iframeRef.current?.contentWindow?.postMessage({
                    type: 'SET_VOLUME',
                    volume: 1.0
                  }, PIPED_FRONTEND);
                }, 1000);
              }
            } catch (error) {
              console.log('Could not communicate with iframe:', error);
            }
          }}
        />
      </div>
    );
  }

  // Audio-only fallback - no iframe, just visual indicator
  return (
    <div className={`relative ${className}`}>
      <div className="w-full h-full bg-gradient-to-br from-purple-900 to-blue-900 flex items-center justify-center text-white p-4">
        <div className="text-center max-w-full">
          <div className="text-4xl mb-3">🎵</div>
          <h3 className="font-bold text-lg mb-2 px-2 break-words leading-tight overflow-hidden"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical' as const
              }}>{title}</h3>
          <div className="text-sm text-white/80 px-2">
            Requested by {requestedBy} • {formatDuration(duration)}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PipedPlayer;
