import { useEffect, useRef } from 'react';

interface PipedPlayerProps {
  videoId: string;
  autoplay?: boolean;
  controls?: boolean;
  expanded?: boolean;
  title?: string;
  className?: string;
}

const PipedPlayer: React.FC<PipedPlayerProps> = ({
  videoId,
  autoplay = true,
  controls = true,
  expanded = true,
  title = 'Video Player',
  className = ''
}) => {
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const PIPED_FRONTEND = 'https://piped.adminforge.de'; // Custom Piped frontend instance
  const PIPED_API = 'https://pipedapi.adminforge.de'; // Custom Piped API instance

  useEffect(() => {
    // Set the Piped instance in the parent window's localStorage
    // This will be used by Piped when it loads
    try {
      localStorage.setItem('instance', PIPED_API);
      localStorage.setItem('pipedInstance', PIPED_API);
      console.log('Piped API instance set to:', PIPED_API);
    } catch (error) {
      console.log('Could not set Piped instance in localStorage:', error);
    }
  }, []);

  // Use the custom Piped frontend for embedding
  const embedUrl = `${PIPED_FRONTEND}/embed/${videoId}?autoplay=${autoplay ? 1 : 0}&controls=${controls ? 1 : 0}`;

  return (
    <iframe
      ref={iframeRef}
      width="100%"
      height="100%"
      src={embedUrl}
      title={title}
      frameBorder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowFullScreen
      className={className}
      onLoad={() => {
        console.log('Piped player loaded for video:', videoId, 'using instance:', PIPED_FRONTEND);

        // Try to communicate with the iframe to set the API instance
        try {
          if (iframeRef.current?.contentWindow) {
            // Post message to set the instance
            iframeRef.current.contentWindow.postMessage({
              type: 'SET_INSTANCE',
              instance: PIPED_API
            }, PIPED_FRONTEND);
          }
        } catch (error) {
          console.log('Could not communicate with iframe:', error);
        }
      }}
    />
  );
};

export default PipedPlayer;
