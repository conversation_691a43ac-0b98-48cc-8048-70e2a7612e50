import { useEffect, useRef, useState, useCallback } from 'react';

interface PipedPlayerProps {
  videoId: string;
  autoplay?: boolean;
  controls?: boolean;
  audioOnly?: boolean; // New prop for audio-only mode
  title?: string;
  className?: string;
  duration?: number; // Expected duration in seconds
  onSongEnd?: () => void; // Callback when song finishes
}

interface PipedVideoData {
  videoStreams: Array<{
    url: string;
    quality: string;
    mimeType: string;
  }>;
  audioStreams: Array<{
    url: string;
    quality: string;
    mimeType: string;
  }>;
  title: string;
  uploader: string;
}

// Global state to persist video data and playback state
const videoDataCache = new Map<string, PipedVideoData>();
const playbackStateCache = new Map<string, { currentTime: number; volume: number }>();

const PipedPlayer: React.FC<PipedPlayerProps> = ({
  videoId,
  autoplay = true,
  controls = true, // Keep for interface compatibility but always use false
  audioOnly = true, // Default to audio-only mode
  title = 'Video Player',
  className = '',
  duration = 0,
  onSongEnd
}) => {
  // Suppress unused variable warning - controls kept for interface compatibility
  void controls;
  const videoRef = useRef<HTMLVideoElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);
  const autoSkipTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const [useDirectStream, setUseDirectStream] = useState(false);
  const [videoData, setVideoData] = useState<PipedVideoData | null>(null);
  const [loading, setLoading] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);

  const PIPED_FRONTEND = 'https://pipedapi.drgns.space'; // Custom Piped frontend instance
  const PIPED_API = 'https://pipedapi.drgns.space'; // Custom Piped API instance

  useEffect(() => {
    // Set the Piped instance in the parent window's localStorage
    try {
      localStorage.setItem('instance', PIPED_API);
      localStorage.setItem('pipedInstance', PIPED_API);
      console.log('Piped API instance set to:', PIPED_API);
    } catch (error) {
      console.log('Could not set Piped instance in localStorage:', error);
    }
  }, []);

  // Auto-skip functionality
  const handleAutoSkip = useCallback(() => {
    if (duration > 0 && onSongEnd) {
      const skipTime = (duration + 5) * 1000; // duration + 5 seconds in milliseconds
      console.log(`Setting auto-skip for ${skipTime}ms`);

      autoSkipTimeoutRef.current = setTimeout(() => {
        console.log('Auto-skipping to next song');
        onSongEnd();
      }, skipTime);
    }
  }, [duration, onSongEnd]);

  // Clear auto-skip timeout
  const clearAutoSkip = useCallback(() => {
    if (autoSkipTimeoutRef.current) {
      clearTimeout(autoSkipTimeoutRef.current);
      autoSkipTimeoutRef.current = null;
    }
  }, []);

  // Try to fetch video data directly from Piped API
  useEffect(() => {
    const fetchVideoData = async () => {
      if (!videoId) return;

      // Check cache first
      const cachedData = videoDataCache.get(videoId);
      if (cachedData) {
        setVideoData(cachedData);
        setUseDirectStream(true);
        setLoading(false);
        console.log('Using cached video data for:', videoId);
        return;
      }

      setLoading(true);
      try {
        console.log('Attempting to fetch video data from Piped API...');
        const response = await fetch(`${PIPED_API}/streams/${videoId}`);

        if (response.ok) {
          const data: PipedVideoData = await response.json();
          // Cache the data
          videoDataCache.set(videoId, data);
          setVideoData(data);
          setUseDirectStream(true);
          console.log('Successfully fetched video data from Piped API:', data.title);
        } else {
          console.log('Piped API not available, falling back to iframe embed');
          setUseDirectStream(false);
        }
      } catch (error) {
        console.log('Error fetching from Piped API, using iframe fallback:', error);
        setUseDirectStream(false);
      } finally {
        setLoading(false);
      }
    };

    fetchVideoData();
  }, [videoId, PIPED_API]);

  // Setup video/audio element when it loads
  useEffect(() => {
    const mediaElement = videoRef.current;
    if (!mediaElement || !useDirectStream) return;

    const handleLoadedMetadata = () => {
      // Set volume to maximum
      mediaElement.volume = 1.0;

      // Restore playback state if available
      const savedState = playbackStateCache.get(videoId);
      if (savedState) {
        mediaElement.currentTime = savedState.currentTime;
        mediaElement.volume = savedState.volume;
        console.log('Restored playback state for:', videoId);
      }

      // Start auto-skip timer
      handleAutoSkip();
    };

    const handleTimeUpdate = () => {
      const time = mediaElement.currentTime;
      setCurrentTime(time);

      // Save playback state
      playbackStateCache.set(videoId, {
        currentTime: time,
        volume: mediaElement.volume
      });
    };

    const handleEnded = () => {
      console.log('Video ended naturally');
      clearAutoSkip();
      if (onSongEnd) {
        onSongEnd();
      }
    };

    const handleError = () => {
      console.log('Video playback error, skipping...');
      clearAutoSkip();
      if (onSongEnd) {
        onSongEnd();
      }
    };

    mediaElement.addEventListener('loadedmetadata', handleLoadedMetadata);
    mediaElement.addEventListener('timeupdate', handleTimeUpdate);
    mediaElement.addEventListener('ended', handleEnded);
    mediaElement.addEventListener('error', handleError);

    return () => {
      mediaElement.removeEventListener('loadedmetadata', handleLoadedMetadata);
      mediaElement.removeEventListener('timeupdate', handleTimeUpdate);
      mediaElement.removeEventListener('ended', handleEnded);
      mediaElement.removeEventListener('error', handleError);
    };
  }, [videoId, useDirectStream, handleAutoSkip, clearAutoSkip, onSongEnd]);

  // For iframe fallback, set up auto-skip
  useEffect(() => {
    if (!useDirectStream && !loading) {
      handleAutoSkip();
      return () => clearAutoSkip();
    }
  }, [useDirectStream, loading, handleAutoSkip, clearAutoSkip]);

  // Cleanup on unmount or video change
  useEffect(() => {
    return () => {
      clearAutoSkip();
    };
  }, [videoId, clearAutoSkip]);

  // Render logic - all hooks are called above this point
  const embedUrl = `${PIPED_FRONTEND}/embed/${videoId}?autoplay=${autoplay ? 1 : 0}&controls=0`; // Remove controls

  // If we have direct stream data, use HTML5 audio/video player
  if (useDirectStream && videoData && !loading) {
    const audioStream = videoData.audioStreams.find(s => s.mimeType.includes('mp4')) || videoData.audioStreams[0];
    const videoStream = videoData.videoStreams.find(s => s.quality === '720p') ||
      videoData.videoStreams.find(s => s.quality === '480p') ||
      videoData.videoStreams[0];

    const mediaProps = {
      ref: videoRef,
      width: "100%",
      height: "100%",
      controls: false, // Remove controls to hide duration/volume bars
      autoPlay: autoplay,
      className: "w-full h-full",
      crossOrigin: "anonymous" as const,
      style: {
        backgroundColor: '#000',
        objectFit: 'contain' as const
      }
    };

    return (
      <div className={`relative ${className}`}>
        {audioOnly ? (
          // Always use audio-only mode
          <audio {...mediaProps}>
            <source src={audioStream.url} type={audioStream.mimeType} />
            Your browser does not support the audio tag.
          </audio>
        ) : (
          // Video mode for popup
          <video {...mediaProps}>
            <source src={videoStream.url} type={videoStream.mimeType} />
            <source src={audioStream.url} type={audioStream.mimeType} />
            Your browser does not support the video tag.
          </video>
        )}

        {/* Audio-only overlay */}
        {audioOnly && (
          <div className="absolute inset-0 flex items-center justify-center bg-gradient-to-br from-purple-900 to-blue-900 text-white">
            <div className="text-center p-4">
              <div className="text-4xl mb-2">🎵</div>
              <h3 className="font-bold text-lg mb-1">{videoData.title}</h3>
              <div className="text-sm opacity-75 mb-2">
                {Math.floor(currentTime / 60)}:{(Math.floor(currentTime % 60)).toString().padStart(2, '0')} / {Math.floor(duration / 60)}:{(Math.floor(duration % 60)).toString().padStart(2, '0')}
              </div>
              <div className="text-xs opacity-50">Audio Only Mode</div>
            </div>
          </div>
        )}

        {/* Video mode overlay */}
        {!audioOnly && (
          <div className="absolute bottom-4 left-4 bg-black bg-opacity-50 text-white text-sm px-2 py-1 rounded">
            {Math.floor(currentTime / 60)}:{(Math.floor(currentTime % 60)).toString().padStart(2, '0')} / {Math.floor(duration / 60)}:{(Math.floor(duration % 60)).toString().padStart(2, '0')}
          </div>
        )}
      </div>
    );
  }

  // Fallback to iframe embed
  return (
    <div className={`relative ${className}`}>
      {audioOnly ? (
        // Audio-only fallback - hide iframe and show audio indicator
        <div className="w-full h-full bg-gradient-to-br from-purple-900 to-blue-900 flex items-center justify-center text-white">
          <div className="text-center p-4">
            <div className="text-4xl mb-2">🎵</div>
            <h3 className="font-bold text-lg mb-1">{title}</h3>
            <div className="text-xs opacity-50">Audio Only Mode (Fallback)</div>
          </div>
          {/* Hidden iframe for audio */}
          <iframe
            ref={iframeRef}
            width="1"
            height="1"
            src={embedUrl}
            title={title}
            style={{ border: 0, position: 'absolute', left: '-9999px' }}
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            onLoad={() => {
              console.log('Piped iframe loaded for audio-only:', videoId);
              try {
                if (iframeRef.current?.contentWindow) {
                  iframeRef.current.contentWindow.postMessage({
                    type: 'SET_INSTANCE',
                    instance: PIPED_API
                  }, PIPED_FRONTEND);

                  setTimeout(() => {
                    iframeRef.current?.contentWindow?.postMessage({
                      type: 'SET_VOLUME',
                      volume: 1.0
                    }, PIPED_FRONTEND);
                  }, 1000);
                }
              } catch (error) {
                console.log('Could not communicate with iframe:', error);
              }
            }}
          />
        </div>
      ) : (
        // Video mode fallback
        <iframe
          ref={iframeRef}
          width="100%"
          height="100%"
          src={embedUrl}
          title={title}
          style={{ border: 0 }}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
          onLoad={() => {
            console.log('Piped iframe loaded for video:', videoId, 'using instance:', PIPED_FRONTEND);

            try {
              if (iframeRef.current?.contentWindow) {
                iframeRef.current.contentWindow.postMessage({
                  type: 'SET_INSTANCE',
                  instance: PIPED_API
                }, PIPED_FRONTEND);

                setTimeout(() => {
                  iframeRef.current?.contentWindow?.postMessage({
                    type: 'SET_VOLUME',
                    volume: 1.0
                  }, PIPED_FRONTEND);
                }, 1000);
              }
            } catch (error) {
              console.log('Could not communicate with iframe:', error);
            }
          }}
        />
      )}
    </div>
  );
};

export default PipedPlayer;
