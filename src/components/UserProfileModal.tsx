import { useEffect, useState } from 'react';
import { X, User, MessageCircle, Calendar, Crown, Star, Trophy, Clock, Hash, Shield, Heart } from 'lucide-react';

type UserProfileModalProps = {
  isOpen: boolean;
  onClose: () => void;
  userProfile: {
    username: string;
    displayName?: string;
    messages: any[];
    stats: {
      totalMessages: number;
      firstSeen: string;
      lastSeen: string;
      isMod: boolean;
      isSubscriber: boolean;
      isVip: boolean;
      isFollower: boolean;
      accountAge?: string;
      followDate?: string;
      subTier?: number;
      subStreak?: number;
    };
    badges?: string[];
    recentActivity?: {
      lastMessage: string;
      messageFrequency: number;
      avgWordsPerMessage: number;
    };
  } | null;
  onRefresh: (username: string) => void;
  isWinnerModal?: boolean;
  onSelectWinner?: () => void;
  onReroll?: () => void;
};

const UserProfileModal = ({
  isOpen,
  onClose,
  userProfile,
  onRefresh,
  isWinnerModal = false,
  onSelectWinner,
  onReroll
}: UserProfileModalProps) => {
  const [refreshInterval, setRefreshInterval] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (isOpen && userProfile) {
      // Refresh user data every 3 seconds
      const interval = setInterval(() => {
        onRefresh(userProfile.username);
      }, 3000);
      setRefreshInterval(interval);

      return () => {
        if (interval) clearInterval(interval);
      };
    } else {
      if (refreshInterval) {
        clearInterval(refreshInterval);
        setRefreshInterval(null);
      }
    }
  }, [isOpen, userProfile, onRefresh]);

  if (!isOpen || !userProfile) return null;

  const formatTime = (timestamp: number) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString();
  };

  const getBadgeIcon = (badge: string) => {
    switch (badge.toLowerCase()) {
      case 'moderator': return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'subscriber': return <Star className="w-4 h-4 text-purple-500" />;
      case 'vip': return <Shield className="w-4 h-4 text-green-500" />;
      case 'follower': return <Heart className="w-4 h-4 text-red-500" />;
      default: return <Hash className="w-4 h-4 text-gray-400" />;
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 backdrop-blur-sm">
      <div className={`bg-gray-900 rounded-2xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden border ${
        isWinnerModal ? 'border-yellow-500 shadow-yellow-500/20' : 'border-gray-700'
      }`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b ${
          isWinnerModal ? 'bg-gradient-to-r from-yellow-900/50 to-orange-900/50 border-yellow-700' : 'border-gray-700'
        }`}>
          <div className="flex items-center space-x-4">
            <div className={`w-16 h-16 rounded-full flex items-center justify-center ${
              isWinnerModal
                ? 'bg-gradient-to-r from-yellow-500 to-orange-500'
                : 'bg-gradient-to-r from-purple-500 to-pink-500'
            }`}>
              {isWinnerModal ? (
                <Trophy className="w-8 h-8 text-white" />
              ) : (
                <User className="w-8 h-8 text-white" />
              )}
            </div>
            <div>
              <h2 className="text-2xl font-bold text-white flex items-center space-x-2">
                <span>{userProfile.displayName || userProfile.username}</span>
                {isWinnerModal && <Trophy className="w-6 h-6 text-yellow-500" />}
              </h2>
              <p className="text-gray-400 text-sm">@{userProfile.username}</p>
              <div className="flex items-center space-x-2 mt-1">
                {userProfile.stats.isMod && (
                  <span className="flex items-center space-x-1 bg-yellow-900/30 px-2 py-1 rounded-full text-xs">
                    <Crown className="w-3 h-3 text-yellow-500" />
                    <span className="text-yellow-300">Moderator</span>
                  </span>
                )}
                {userProfile.stats.isSubscriber && (
                  <span className="flex items-center space-x-1 bg-purple-900/30 px-2 py-1 rounded-full text-xs">
                    <Star className="w-3 h-3 text-purple-500" />
                    <span className="text-purple-300">
                      {userProfile.stats.subTier ? `Tier ${userProfile.stats.subTier} Sub` : 'Subscriber'}
                    </span>
                  </span>
                )}
                {userProfile.stats.isVip && (
                  <span className="flex items-center space-x-1 bg-green-900/30 px-2 py-1 rounded-full text-xs">
                    <Shield className="w-3 h-3 text-green-500" />
                    <span className="text-green-300">VIP</span>
                  </span>
                )}
                {userProfile.stats.isFollower && (
                  <span className="flex items-center space-x-1 bg-red-900/30 px-2 py-1 rounded-full text-xs">
                    <Heart className="w-3 h-3 text-red-500" />
                    <span className="text-red-300">Follower</span>
                  </span>
                )}
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            {isWinnerModal && (
              <div className="text-right">
                <p className="text-yellow-400 font-bold text-lg">🎉 WINNER! 🎉</p>
                <p className="text-gray-400 text-sm">Congratulations!</p>
              </div>
            )}
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-white transition-colors p-2 hover:bg-gray-700 rounded-full"
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Enhanced Stats */}
        <div className="p-6 border-b border-gray-700">
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-white flex items-center justify-center space-x-1">
                <MessageCircle className="w-5 h-5 text-blue-400" />
                <span>{userProfile.stats.totalMessages}</span>
              </div>
              <div className="text-sm text-gray-400">Messages</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white flex items-center justify-center space-x-1">
                <Calendar className="w-5 h-5 text-green-400" />
                <span>{userProfile.stats.firstSeen}</span>
              </div>
              <div className="text-sm text-gray-400">First Seen</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-white flex items-center justify-center space-x-1">
                <Clock className="w-5 h-5 text-orange-400" />
                <span>{userProfile.stats.lastSeen}</span>
              </div>
              <div className="text-sm text-gray-400">Last Seen</div>
            </div>
            {userProfile.stats.followDate && (
              <div className="text-center">
                <div className="text-2xl font-bold text-white flex items-center justify-center space-x-1">
                  <Heart className="w-5 h-5 text-red-400" />
                  <span>{userProfile.stats.followDate}</span>
                </div>
                <div className="text-sm text-gray-400">Follow Date</div>
              </div>
            )}
            {userProfile.stats.subStreak && (
              <div className="text-center">
                <div className="text-2xl font-bold text-white flex items-center justify-center space-x-1">
                  <Star className="w-5 h-5 text-purple-400" />
                  <span>{userProfile.stats.subStreak}</span>
                </div>
                <div className="text-sm text-gray-400">Sub Streak</div>
              </div>
            )}
            {userProfile.recentActivity && (
              <div className="text-center">
                <div className="text-2xl font-bold text-white">
                  {userProfile.recentActivity.messageFrequency.toFixed(1)}
                </div>
                <div className="text-sm text-gray-400">Msgs/Hour</div>
              </div>
            )}
          </div>
        </div>

        {/* Recent Messages */}
        <div className="p-6">
          <div className="flex items-center space-x-2 mb-4">
            <MessageCircle className="w-5 h-5 text-gray-400" />
            <h3 className="text-lg font-semibold text-white">Recent Messages</h3>
            <span className="text-sm text-gray-400">
              (Updates every 3s)
            </span>
          </div>

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {userProfile.messages.length > 0 ? (
              userProfile.messages.slice(-20).reverse().map((message, index) => (
                <div key={index} className="bg-gray-700 rounded-lg p-3">
                  <div className="flex items-center justify-between mb-1">
                    <span className="text-sm text-gray-400">
                      {formatDate(message.timestamp)} at {formatTime(message.timestamp)}
                    </span>
                    <div className="flex items-center space-x-1">
                      {message.isMod && (
                        <Crown className="w-3 h-3 text-yellow-500" title="Moderator" />
                      )}
                      {message.isSubscriber && (
                        <Star className="w-3 h-3 text-purple-500" title="Subscriber" />
                      )}
                    </div>
                  </div>
                  <p className="text-white text-sm">{message.message}</p>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-400 py-8">
                <MessageCircle className="w-12 h-12 mx-auto mb-2 opacity-50" />
                <p>No messages found for this user</p>
              </div>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className={`px-6 py-4 flex justify-between items-center ${
          isWinnerModal ? 'bg-gradient-to-r from-yellow-900/30 to-orange-900/30' : 'bg-gray-700'
        }`}>
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <Clock className="w-4 h-4" />
            <span>Updates every 3s</span>
          </div>

          <div className="flex items-center space-x-3">
            {isWinnerModal && onSelectWinner && (
              <>
                <button
                  onClick={() => {
                    console.log('🎯 UserProfileModal: Confirm Winner button clicked');
                    onSelectWinner();
                  }}
                  className="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-500 transition-colors flex items-center space-x-2"
                >
                  <Trophy className="w-4 h-4" />
                  <span>Confirm Winner</span>
                </button>
                {onReroll && (
                  <button
                    onClick={() => {
                      console.log('🎯 UserProfileModal: Reroll button clicked');
                      onReroll();
                    }}
                    className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-500 transition-colors"
                  >
                    Reroll
                  </button>
                )}
              </>
            )}
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-500 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfileModal;
